# CogBridges - Reddit智能搜索分析平台

<div align="center">

![CogBridges Logo](https://via.placeholder.com/200x80/4A90E2/FFFFFF?text=CogBridges)

**基于Google Custom Search API和Reddit API的Reddit数据智能分析平台**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.0+-green.svg)](https://flask.palletsprojects.com)
[![Google API](https://img.shields.io/badge/Google-Custom%20Search-red.svg)](https://developers.google.com/custom-search)
[![Reddit API](https://img.shields.io/badge/Reddit-API-orange.svg)](https://www.reddit.com/dev/api)

[快速开始](#-快速开始) • [技术架构](#-技术架构) • [API接口](#-api接口) • [测试验证](#-测试验证)

</div>

---

## 🎯 项目简介

**CogBridges** 是一个基于Google Custom Search API和Reddit API的智能搜索分析平台，实现了从Google搜索到Reddit数据获取，再到LLM驱动的用户画像分析和动机洞察的完整业务流程。

### 🌟 核心特性

- **🔍 Google精准搜索**：集成Google Custom Search API，实现对Reddit内容的精准定位。
- **📊 Reddit深度数据**：并行获取Reddit帖子、评论及用户历史数据。
- **🧠 LLM智能分析**：
  - **相似Subreddit筛选**：利用大语言模型分析用户兴趣，识别相似的subreddits。
  - **用户动机洞察**：深度分析用户评论动机，构建精准的用户画像。
- **⚡ 异步高效处理**：采用`asyncio`进行高效的并发数据获取和LLM调用。
- **🌐 统一Web界面**：提供现代化的前端界面，支持实时搜索和结构化结果展示。
- **🔧 灵活配置**：通过`.env`文件和`config.py`模块，轻松管理API密钥和应用参数。
- **🚀 一键启动**：使用`start_cogbridges.py`脚本，一键启动后端API和前端Web服务。

---

## 🏗 技术架构

### 核心业务流程

```mermaid
graph TD
    A[用户在Web界面输入查询] --> B{CogBridges后端API}
    B --> C[步骤1: Google搜索]
    C --> D[步骤2: Reddit数据获取]
    D --> E[步骤3: 用户历史分析]
    E --> F[步骤4: LLM相似性筛选]
    F --> G[步骤5: LLM动机分析]
    G --> H[生成统一分析json数据]
    H --> I[在Web界面展示结果]
```

### 五大核心步骤

| 步骤 | 功能描述 | 技术实现 |
|------|----------|----------|
| **步骤1: Google搜索** | 使用Google Custom Search API搜索Reddit相关内容 | `google-api-python-client` |
| **步骤2: Reddit数据获取** | 并行获取Reddit帖子内容和评论数据 | `asyncpraw` (异步Reddit API库) |
| **步骤3: 用户分析** | 分析评论者历史，构建用户活动概览 | 用户历史数据分析 |
| **步骤4: LLM相似性筛选** | 使用大语言模型筛选与目标subreddit相似的子版块 | Replicate API + `meta/meta-llama-3-8b-instruct` |
| **步骤5: LLM动机分析** | 结合用户历史、相似社区和评论内容，分析用户动机 | Replicate API + 结构化分析Prompt |

---

## 🚀 快速开始

### 环境要求

- **Python**: 3.8+
- **系统**: Windows/Linux/macOS
- **网络**: 需要访问Google API、Reddit API和Replicate API

### 安装步骤

```bash
# 1. 克隆项目
git clone <repository-url>
cd CogBridges_v020

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置API密钥（核心步骤）

# 使用.env文件进行配置
# 首先，复制环境变量模板文件
cp .env.example .env

# 然后，编辑 .env 文件，填入你的API密钥
# nano .env 或 vim .env
```

`.env` 文件内容示例：

```plaintext
# Google Custom Search API
GOOGLE_SEARCH_API_KEY="your_google_api_key"
GOOGLE_SEARCH_ENGINE_ID="your_google_search_engine_id"

# Reddit API
REDDIT_CLIENT_ID="your_reddit_client_id"
REDDIT_CLIENT_SECRET="your_reddit_client_secret"
REDDIT_USER_AGENT="CogBridges/2.0 by YourUsername"

# Replicate API (用于LLM功能)
REPLICATE_API_TOKEN="your_replicate_api_token"
```

**安全提醒**：
- `.env` 文件已添加到 `.gitignore`，确保不会被提交到版本控制。
- 妥善保管API密钥，避免泄露。

```bash
# 4. 验证配置（可选）
python config.py

# 5. 一键启动应用
python start_cogbridges.py
```

### 验证安装

启动成功后，系统会自动：
- 启动后端API服务器 (默认: `http://127.0.0.1:5000`)
- 启动前端Web服务器 (默认: `http://localhost:5001`)
- 自动在浏览器中打开前端Web界面

你可以访问 `http://127.0.0.1:5000/api/health` 检查API服务状态。

---

## 🔧 API接口

### 核心搜索接口

#### `POST /api/search`

执行完整的搜索和分析流程。

**请求体**:
```json
{
  "query": "搜索查询内容",
  "llm_analysis": true,
  "enhanced_comments": true
}
```

**响应示例**:
```json
{
  "success": true,
  "query": "Should I subscribe to GPT, Claude, or Gemini?",
  "session_id": "20240725_103000_abcdef12",
  "timestamp": "2024-07-25T10:30:45.123Z",
  "total_time": 65.8,
  "google_results": [...],
  "reddit_posts": [...],
  "commenters_history": {...},
  "llm_analysis": {
    "similarity_analysis": {
      "user123": {
        "original_subreddits": ["Python", "Flask", "MachineLearning"],
        "target_analysis": {
          "ClaudeAI": {
            "similar_subreddits": ["MachineLearning"],
            "similarity_count": 1
          }
        },
        "total_similar_count": 1
      }
    },
    "motivation_analysis": {
      "user123": [
        {
          "user_profile": "技术爱好者",
          "comment_motivation": "分享使用经验",
          "key_factors": ["长期使用", "技术对比"]
        }
      ]
    },
    "analysis_summary": {
      "total_users_analyzed": 5,
      "total_motivations_analyzed": 8,
      "top_similar_subreddits": {"MachineLearning": 5, "AI": 3},
      "user_type_distribution": {"developer": 3, "learner": 2},
      "key_insights": [
        "分析了5个用户的subreddit相似性",
        "最常见的相似subreddit是MachineLearning"
      ]
    }
  },
  "statistics": {
    "google_search_time": 2.1,
    "reddit_posts_time": 15.4,
    "commenters_history_time": 25.2,
    "llm_analysis_time": 23.1,
    "google_results_count": 5,
    "reddit_posts_count": 3,
    "commenters_count": 12,
    "similarity_analysis_count": 5,
    "motivation_analysis_count": 8
  }
}
```

### 健康检查与状态接口

#### `GET /api/health`
检查API服务是否正常运行。

**响应**:
```json
{
  "status": "healthy",
  "service": "CogBridges API",
  "timestamp": **********.0
}
```

#### `GET /api/status`
获取服务的详细状态，包括各依赖服务的配置情况。

**响应**:
```json
{
  "service": "CogBridges API",
  "version": "2.0.0",
  "features": {
    "basic_search": true,
    "enhanced_comments": true,
    "llm_analysis": true
  },
  "services": {
    "google_search": true,
    "reddit_api": true,
    "llm_service": true
  },
  "statistics": {...}
}
```

---

## 🧠 LLM智能分析功能

### 功能概述

CogBridges集成了基于Replicate API的大语言模型分析功能，提供两个核心能力：

#### 1. 相似Subreddit筛选
- **功能**：从用户的subreddit历史中，筛选出与当前讨论主题相似的子版块。
- **应用场景**：识别用户的核心兴趣圈，为动机分析提供关键上下文。
- **技术实现**：使用`meta/meta-llama-3-8b-instruct`模型进行语义相似性判断。

#### 2. 用户评论动机分析
- **功能**：分析用户在特定帖子中发表评论背后的动机和用户画像。
- **应用场景**：深度理解用户行为，构建更精准的用户画像，洞察用户需求。
- **技术实现**：结合用户历史、相似subreddit数据、评论内容，通过结构化Prompt进行综合分析。

### 分析流程

```mermaid
graph LR
    A[用户历史数据] --> B[提取Subreddit列表]
    B --> C{LLM相似性筛选}
    C --> D[构建相似Subreddit上下文]
    D --> E{LLM动机分析}
    A & C & E --> F[生成用户画像和动机报告]
```

### 配置要求

确保在`.env`文件中正确配置`REPLICATE_API_TOKEN`。

```plaintext
# .env文件
REPLICATE_API_TOKEN="your_replicate_api_token"
```

如果未配置，LLM分析功能将自动跳过。

---

## 🧪 测试验证

### 运行完整集成测试

我们提供了一个全面的端到端集成测试脚本，模拟完整的业务流程。

```bash
# 运行集成测试
python test_integration_complete.py
```

该测试将执行以下步骤：
1. 使用预设查询进行搜索。
2. 验证Google搜索、Reddit数据获取和用户分析功能。
3. 检查LLM相似性筛选和动机分析是否成功执行。
4. 生成详细的测试报告和性能指标，并保存为JSON文件。

### 专项功能测试

除了集成测试，我们还提供针对特定功能的测试脚本：

- `test_comment_motivation.py`: 专注于测试LLM用户评论动机分析功能。
- `test_start_cogbridges_llm.py`: 专门用于测试`start_cogbridges.py`中的LLM分析流程。

运行示例：
```bash
python test_comment_motivation.py
```

---

## ⚙️ 配置说明

所有配置项均通过`.env`文件加载到`config.py`模块中。这种方式实现了配置与代码的分离，提高了安全性。

### 主要配置项 (`config.py`)

- **`GOOGLE_API_KEY`**: Google Custom Search API密钥。
- **`GOOGLE_SEARCH_ENGINE_ID`**: Google Programmable Search Engine ID。
- **`REDDIT_CLIENT_ID`**: Reddit API应用客户端ID。
- **`REDDIT_CLIENT_SECRET`**: Reddit API应用客户端密钥。
- **`REPLICATE_API_TOKEN`**: Replicate API令牌，用于LLM功能。
- **`HOST` / `PORT`**: 后端API服务器的地址和端口。
- **`ENABLE_PROXY` / `HTTP_PROXY`**: 代理设置。

### 获取API密钥

- **Google Custom Search API**:
  1. 访问 [Google Cloud Console](https://console.cloud.google.com/) 创建项目并启用 "Custom Search API"。
  2. 创建API密钥。
  3. 访问 [Programmable Search Engine](https://programmablesearchengine.google.com/) 设置搜索引擎，并关联到你的网站（如`reddit.com`）。

- **Reddit API**:
  1. 访问 [Reddit App Preferences](https://www.reddit.com/prefs/apps)。
  2. 创建一个 "script" 类型的应用以获取`client_id`和`client_secret`。

- **Replicate API**:
  1. 访问 [Replicate](https://replicate.com/) 注册并获取API Token。

---

## 🚀 部署指南

### 本地开发

```bash
# 1. 配置.env文件
# 2. 启动应用
python start_cogbridges.py
```

### 生产部署 (推荐)

#### 使用Gunicorn

```bash
# 安装Gunicorn
pip install gunicorn

# 启动生产服务 (假设你的Flask app实例在start_cogbridges.py中名为app)
# 注意：你需要修改start_cogbridges.py，使其能被Gunicorn调用
# 例如，创建一个app.py来承载Flask应用实例
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

#### Docker部署

项目根目录下提供了`Dockerfile`，可以方便地进行容器化部署。

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 复制依赖文件并安装
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制所有项目文件
COPY . .

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "start_cogbridges.py"]
```

构建和运行Docker容器：
```bash
# 构建镜像
docker build -t cogbridges .

# 运行容器
docker run -p 5000:5000 --env-file .env cogbridges
```
**注意**: `--env-file` 参数可以方便地将`.env`文件中的环境变量传递给容器。

---

## 🔧 故障排查

### 1. API密钥配置问题
运行`python config.py`脚本，它会输出当前的配置状态，帮助你检查API密钥是否加载成功。

### 2. 网络连接问题
- **代理**: 如果你所在地区无法直接访问Google或Replicate，请在`.env`文件中配置`ENABLE_PROXY=True`和`HTTP_PROXY`。
- **防火墙**: 确保防火墙没有阻止到`*.googleapis.com`, `www.reddit.com`, `api.replicate.com`的传出连接。

### 3. 依赖安装问题
如果遇到依赖问题，可以尝试创建一个新的虚拟环境并重新安装：
```bash
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
```

---

## 📈 项目特点

### 技术亮点

- **真实API集成**: 完全基于官方API（Google, Reddit, Replicate），非爬虫，稳定可靠。
- **异步优先**: 大量使用`asyncio`和`asyncpraw`，最大化I/O效率。
- **LLM深度应用**: 不仅仅是调用LLM，而是设计了多步骤的分析链，将LLM分析能力与传统数据挖掘结合。
- **模块化设计**: 服务、模型和工具类分离，易于扩展和维护。
- **配置与代码分离**: 使用`.env`管理敏感信息，安全且灵活。

### 应用场景

- **市场调研**: 深度分析Reddit用户对特定产品或服务的讨论热点和情感倾向。
- **用户研究**: 快速构建目标用户群体的兴趣画像和行为动机。
- **内容策略**: 挖掘热门话题和用户互动模式，指导内容创作。
- **竞品分析**: 洞察用户对竞争对手产品的看法和核心需求。

---

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 `LICENSE` 文件。

---

## 📞 联系我们

- **项目主页**: [https://github.com/your-org/cogbridges](https://github.com/your-org/cogbridges)
- **问题反馈**: [https://github.com/your-org/cogbridges/issues](https://github.com/your-org/cogbridges/issues)

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

Made with ❤️ by the CogBridges Team

**CogBridges** - 洞察Reddit，连接智慧 🔍✨

</div>
