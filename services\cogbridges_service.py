"""
CogBridges Search - 核心业务流程服务
实现完整的串行业务流程：Google搜索 -> Reddit帖子获取 -> 评论者历史数据获取
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field

from config import config
from services.google_search_api import GoogleSearchService
from services.reddit_service import RedditService
from services.data_service import DataService
from services.llm_service import llm_service

from models.reddit_models import RedditPost, RedditComment
from utils.logger_utils import get_logger
# from utils.performance_monitor import performance_monitor, PerformanceContext


@dataclass
class CogBridgesSearchResult:
    """CogBridges搜索结果"""
    query: str  # 原始查询
    session_id: str
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 翻译信息
    translated_query: str = ""  # 翻译后的查询
    translation_time: float = 0.0  # 翻译耗时
    
    # 步骤1: Google搜索结果
    google_results: List[Dict[str, Any]] = field(default_factory=list)
    google_search_time: float = 0.0
    
    # 步骤2: Reddit帖子数据
    reddit_posts: List[Dict[str, Any]] = field(default_factory=list)
    reddit_posts_time: float = 0.0
    
    # 步骤3: 评论者历史数据
    commenters_history: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    commenters_history_time: float = 0.0



    # 总体统计
    total_time: float = 0.0
    success: bool = True
    error_message: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "query": self.query,
            "translated_query": self.translated_query,
            "session_id": self.session_id,
            "timestamp": self.timestamp.isoformat(),
            "translation_time": self.translation_time,
            "google_results": self.google_results,
            "google_search_time": self.google_search_time,
            "reddit_posts": self.reddit_posts,
            "reddit_posts_time": self.reddit_posts_time,
            "commenters_history": self.commenters_history,
            "commenters_history_time": self.commenters_history_time,
            "total_time": self.total_time,
            "success": self.success,
            "error_message": self.error_message
        }


class CogBridgesService:
    """CogBridges核心业务服务"""
    
    def __init__(self):
        """初始化服务"""
        self.logger = get_logger(__name__)
        
        # 初始化子服务
        self.google_service = GoogleSearchService()
        self.reddit_service = RedditService()
        self.data_service = DataService()
        self.llm_service = llm_service

        
        # 业务参数（可配置）
        self.max_search_results = 5  # 前5个搜索结果
        self.max_comments_per_post = 6  # 每个帖子前6个评论
        self.max_user_comments = 20  # 用户历史前20个评论
        self.max_user_posts = 10  # 用户历史前10个帖子
        
        self.logger.info("CogBridges核心业务服务初始化成功")
    
    async def search(self, query: str) -> CogBridgesSearchResult:
        """
        执行完整的CogBridges搜索流程
        
        Args:
            query: 搜索查询
            
        Returns:
            完整的搜索结果
        """
        start_time = time.time()
        session_id = self.data_service.generate_session_id(query)
        
        result = CogBridgesSearchResult(
            query=query,
            session_id=session_id
        )
        
        try:
            self.logger.info(f"开始CogBridges搜索流程: {query}")
            
            # 步骤0: 翻译查询（如果LLM服务可用）
            translated_query = query
            translation_time = 0.0
            if self.llm_service and self.llm_service.configured:
                try:
                    self.logger.info(f"步骤0: 翻译查询 - {query}")
                    translation_start = time.time()
                    translated_query = await self.llm_service.translate_query_to_english(query)
                    translation_time = time.time() - translation_start
                    self.logger.info(f"步骤0完成: 查询翻译完成, 耗时: {translation_time:.2f}秒")
                except Exception as e:
                    self.logger.warning(f"查询翻译失败，使用原查询: {e}")
                    translated_query = query
            
            # 保存翻译信息到结果中
            result.translated_query = translated_query
            result.translation_time = translation_time
            
            # 步骤1: Google搜索（使用翻译后的查询）
            google_results = await self._step1_google_search(translated_query)
            result.google_results = google_results["results"]
            result.google_search_time = google_results["search_time"]
            
            if not result.google_results:
                result.success = False
                result.error_message = "Google搜索未找到结果"
                return result
            
            # 步骤2: 并行获取Reddit帖子内容和评论
            reddit_data = await self._step2_get_reddit_posts(result.google_results)
            result.reddit_posts = reddit_data["posts"]
            result.reddit_posts_time = reddit_data["processing_time"]
            
            if not result.reddit_posts:
                result.success = False
                result.error_message = "未能获取Reddit帖子数据"
                return result
            
            # 步骤3: 并行获取评论者历史数据
            commenters_data = await self._step3_get_commenters_history(result.reddit_posts)
            result.commenters_history = commenters_data["history"]
            result.commenters_history_time = commenters_data["processing_time"]
            
            # 计算总时间
            result.total_time = time.time() - start_time
            
            # 保存完整结果
            await self._save_results(result)
            
            self.logger.info(f"CogBridges搜索完成: {query}, 总耗时: {result.total_time:.2f}秒")
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            result.total_time = time.time() - start_time
            self.logger.error(f"CogBridges搜索失败: {e}")
        
        return result


    
    async def _step1_google_search(self, query: str) -> Dict[str, Any]:
        """步骤1: 使用Google API搜索内容"""
        self.logger.info(f"步骤1: Google搜索 - {query}")
        start_time = time.time()
        
        search_result = await self.google_service.search(
            query=query,
            max_results=self.max_search_results
        )
        
        if not search_result.success:
            self.logger.warning(f"Google搜索失败: {search_result.error_message}")
            return {"results": [], "search_time": time.time() - start_time}
        
        # 直接使用API返回的结果
        formatted_results = [
            {
                "title": res.title,
                "url": res.url,
                "snippet": res.snippet,
                "rank": res.rank
            }
            for res in search_result.results
        ]
        
        search_time = time.time() - start_time
        self.logger.info(f"步骤1完成: 找到 {len(formatted_results)} 个结果, 耗时: {search_time:.2f}秒")
        
        return {
            "results": formatted_results,
            "search_time": search_time
        }
    
    async def _step2_get_reddit_posts(self, google_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """步骤2: 并行获取Reddit帖子内容和评论"""
        self.logger.info(f"步骤2: 并行获取 {len(google_results)} 个Reddit帖子的内容和评论")
        start_time = time.time()
        
        # 创建并行任务
        tasks = []
        for result in google_results:
            task = self._get_single_post_data(result["url"], result)
            tasks.append(task)
        
        # 并行执行
        posts_data = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        valid_posts = []
        for data in posts_data:
            if isinstance(data, dict) and data.get("success"):
                valid_posts.append(data)
            elif isinstance(data, Exception):
                self.logger.warning(f"获取帖子数据失败: {data}")
        
        processing_time = time.time() - start_time
        self.logger.info(f"步骤2完成: 成功获取 {len(valid_posts)} 个帖子数据, 耗时: {processing_time:.2f}秒")
        
        return {
            "posts": valid_posts,
            "processing_time": processing_time
        }
    
    async def _get_single_post_data(self, url: str, google_result: Dict[str, Any]) -> Dict[str, Any]:
        """获取单个帖子的完整数据"""
        try:
            # 获取帖子详情
            post_details = await self.reddit_service.get_post_details(url)
            if not post_details:
                return {"success": False, "error": "帖子不存在"}
            
            # 获取评论
            comments = await self.reddit_service.get_post_comments(
                url, 
                limit=self.max_comments_per_post
            )
            
            return {
                "success": True,
                "google_result": google_result,
                "post": {
                    "id": post_details["id"],
                    "title": post_details["title"],
                    "author": post_details["author"],
                    "score": post_details["score"],
                    "num_comments": post_details["num_comments"],
                    "subreddit": post_details["subreddit"],
                    "url": post_details["url"],
                    "created_utc": post_details["created_utc"],
                    "selftext": post_details.get("selftext", "")
                },
                "comments": [
                    {
                        "id": c["id"],
                        "author": c["author"],
                        "body": c["body"],
                        "score": c["score"],
                        "created_utc": c["created_utc"]
                    } for c in comments
                ],
                "commenters": list(set(c["author"] for c in comments if c["author"] != "[deleted]"))
            }
            
        except Exception as e:
            self.logger.warning(f"获取帖子数据失败 {url}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _step3_get_commenters_history(self, posts_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """步骤3: 使用新的overview获取方法获取评论者历史数据"""
        self.logger.info("步骤3: 使用新的overview方法获取评论者历史数据")
        start_time = time.time()

        # 收集所有评论者
        all_commenters = set()
        for post_data in posts_data:
            all_commenters.update(post_data["commenters"])

        # 过滤有效评论者
        filtered_commenters = self._filter_valid_commenters(list(all_commenters))
        self.logger.info(f"需要获取 {len(filtered_commenters)} 个评论者的历史数据 (原始: {len(all_commenters)})")

        # 使用新的overview获取方法
        commenters_history = {}
        max_items_per_user = 100  # 每个用户最多100条记录

        # 创建并发任务
        semaphore = asyncio.Semaphore(len(filtered_commenters))  # 最大并发数等于用户数
        
        async def fetch_user_overview(username):
            async with semaphore:
                return await self._get_user_full_overview_history(username, max_items_per_user)

        # 并发执行所有任务
        tasks = [fetch_user_overview(username) for username in filtered_commenters]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for i, result in enumerate(results):
            username = filtered_commenters[i]
            if isinstance(result, dict) and result.get('status') == 'success':
                # 转换格式以兼容原有结构
                commenters_history[username] = self._convert_overview_to_legacy_format(result)
            elif isinstance(result, Exception):
                self.logger.warning(f"获取用户 {username} 历史失败: {result}")

        processing_time = time.time() - start_time
        self.logger.info(f"步骤3完成: 获取了 {len(commenters_history)} 个用户的历史数据, 耗时: {processing_time:.2f}秒")

        return {
            "history": commenters_history,
            "processing_time": processing_time
        }
    

    
    async def _save_results(self, result: CogBridgesSearchResult):
        """保存完整的搜索结果"""
        try:
            # 保存到JSON文件
            filename = f"cogbridges_search_{result.session_id}.json"
            filepath = self.data_service.data_dir / "results" / filename
            
            import json
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result.to_dict(), f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"搜索结果已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存搜索结果失败: {e}")
    
    async def close(self):
        """关闭服务并清理资源"""
        try:
            await self.reddit_service.close()
            self.logger.info("CogBridges服务已关闭")
        except Exception as e:
            self.logger.error(f"关闭CogBridges服务时出错: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        stats = {
            "google_stats": self.google_service.get_statistics(),
            "reddit_stats": self.reddit_service.get_statistics(),
            "business_config": {
                "max_search_results": self.max_search_results,
                "max_comments_per_post": self.max_comments_per_post,
                "max_user_comments": self.max_user_comments,
                "max_user_posts": self.max_user_posts
            }
        }

        # 添加LLM服务统计（如果可用）
        if self.llm_service.configured:
            stats["llm_stats"] = self.llm_service.get_stats()

        return stats

    def _filter_valid_commenters(self, commenters: List[str]) -> List[str]:
        """
        过滤有效的评论者，移除机器人账户和无效用户

        Args:
            commenters: 原始评论者列表

        Returns:
            过滤后的评论者列表
        """
        import re

        filtered = []

        # 常见的机器人账户模式
        bot_patterns = [
            r'.*bot$',
            r'.*_bot$',
            r'bot_.*',
            r'auto.*',
            r'.*moderator.*',
            r'.*admin.*'
        ]

        # 无效用户名模式
        invalid_patterns = [
            r'^\[deleted\]$',
            r'^\[removed\]$',
            r'^deleted$',
            r'^removed$'
        ]

        for commenter in commenters:
            if not commenter or len(commenter.strip()) == 0:
                continue

            commenter_lower = commenter.lower()

            # 检查是否为无效用户
            is_invalid = any(re.match(pattern, commenter_lower) for pattern in invalid_patterns)
            if is_invalid:
                continue

            # 检查是否为机器人（可选，根据需要启用）
            # is_bot = any(re.match(pattern, commenter_lower) for pattern in bot_patterns)
            # if is_bot:
            #     continue

            # 过滤过短或过长的用户名
            if len(commenter) < 3 or len(commenter) > 20:
                continue

            filtered.append(commenter)

        return filtered

    async def _get_user_full_overview_history(self, username: str, max_items: int = 100) -> Dict[str, Any]:
        """获取单个用户的所有历史overview（帖子和评论）"""
        try:
            reddit = await self.reddit_service._ensure_async_reddit()
            redditor = await reddit.redditor(username)
            
            submissions = []
            comments = []
            total_count = 0
            start_time = time.time()
            
            # 获取所有历史overview（包括帖子和评论），按top排序
            async for item in redditor.top(limit=None):
                total_count += 1
                
                # 判断是帖子还是评论
                if hasattr(item, 'is_self') or hasattr(item, 'title'):  # 这是帖子
                    submission_info = await self._get_submission_info(item)
                    submissions.append(submission_info)
                else:  # 这是评论
                    comment_info = await self._get_comment_info(item)
                    # 只保留直接回复帖子的评论
                    if comment_info.get('is_reply_to_submission', False):
                        comments.append(comment_info)
                
                # 设置安全限制，避免无限循环
                if total_count >= max_items:
                    break
            
            end_time = time.time()
            total_time = end_time - start_time
            
            return {
                "username": username,
                "status": "success",
                "total_items": total_count,
                "submissions_count": len(submissions),
                "comments_count": len(comments),
                "total_time": total_time,
                "rate_per_second": total_count/total_time if total_time > 0 else 0,
                "submissions": submissions,
                "comments": comments,
                "reached_limit": total_count >= max_items
            }
            
        except Exception as e:
            self.logger.warning(f"获取用户 {username} overview失败: {e}")
            return {
                "username": username,
                "status": "error",
                "error": str(e),
                "total_items": 0,
                "submissions_count": 0,
                "comments_count": 0,
                "total_time": 0,
                "rate_per_second": 0,
                "submissions": [],
                "comments": [],
                "reached_limit": False
            }

    async def _get_submission_info(self, submission) -> Dict[str, Any]:
        """获取帖子的基本信息"""
        try:
            return {
                "score": getattr(submission, 'score', 0),
                "title": getattr(submission, 'title', 'N/A'),
                "selftext": getattr(submission, 'selftext', 'N/A'),
                "subreddit": getattr(submission, 'subreddit_name_prefixed', 'N/A'),
                "created_utc": getattr(submission, 'created_utc', 0)
            }
        except Exception as e:
            return {
                "error": f"获取帖子信息失败: {str(e)}",
                "score": 0,
                "title": "N/A",
                "selftext": "N/A",
                "subreddit": "N/A",
                "created_utc": 0
            }

    async def _get_comment_info(self, comment) -> Dict[str, Any]:
        """获取评论的基本信息"""
        try:
            # 判断是回复帖子还是评论
            parent_id = getattr(comment, 'parent_id', 'N/A')
            is_reply_to_submission = parent_id.startswith('t3_') if parent_id != 'N/A' else False
            
            return {
                "body": getattr(comment, 'body', 'N/A'),
                "score": getattr(comment, 'score', 0),
                "created_utc": getattr(comment, 'created_utc', 0),
                "subreddit": getattr(comment, 'subreddit_name_prefixed', 'N/A'),
                "is_reply_to_submission": is_reply_to_submission,
                "submission_title": getattr(comment, 'link_title', 'N/A')
            }
        except Exception as e:
            return {
                "error": f"获取评论信息失败: {str(e)}",
                "body": "N/A",
                "score": 0,
                "created_utc": 0,
                "subreddit": "N/A",
                "is_reply_to_submission": False,
                "submission_title": "N/A"
            }

    def _convert_overview_to_legacy_format(self, overview_result: Dict[str, Any]) -> Dict[str, Any]:
        """将新的overview格式转换为原有的legacy格式"""
        if overview_result.get('status') != 'success':
            return {}
        
        legacy_format = {}
        
        # 收集所有子版块
        all_subreddits = []
        all_subreddits.extend([s.get('subreddit', 'N/A') for s in overview_result.get('submissions', []) if s.get('subreddit') != 'N/A'])
        all_subreddits.extend([c.get('subreddit', 'N/A') for c in overview_result.get('comments', []) if c.get('subreddit') != 'N/A'])
        unique_subreddits = list(set(all_subreddits))
        
        # 处理帖子数据
        for submission in overview_result.get('submissions', []):
            subreddit = submission.get('subreddit', 'N/A')
            if subreddit not in legacy_format:
                legacy_format[subreddit] = {"posts": [], "comments": []}
            
            legacy_format[subreddit]["posts"].append({
                "id": f"post_{submission.get('created_utc', 0)}",  # 生成唯一ID
                "title": submission.get('title', 'N/A'),
                "score": submission.get('score', 0),
                "created_utc": submission.get('created_utc', 0)
            })
        
        # 处理评论数据
        for comment in overview_result.get('comments', []):
            subreddit = comment.get('subreddit', 'N/A')
            if subreddit not in legacy_format:
                legacy_format[subreddit] = {"posts": [], "comments": []}
            
            legacy_format[subreddit]["comments"].append({
                "id": f"comment_{comment.get('created_utc', 0)}",  # 生成唯一ID
                "body": comment.get('body', 'N/A')[:200] + "..." if len(comment.get('body', '')) > 200 else comment.get('body', 'N/A'),
                "score": comment.get('score', 0),
                "created_utc": comment.get('created_utc', 0)
            })
        
        # 添加子版块列表信息
        legacy_format["_metadata"] = {
            "subreddits": unique_subreddits,
            "unique_subreddits_count": len(unique_subreddits),
            "total_posts": len(overview_result.get('submissions', [])),
            "total_comments": len(overview_result.get('comments', [])),
            "username": overview_result.get('username', ''),
            "total_items": overview_result.get('total_items', 0),
            "rate_per_second": overview_result.get('rate_per_second', 0)
        }
        
        return legacy_format
