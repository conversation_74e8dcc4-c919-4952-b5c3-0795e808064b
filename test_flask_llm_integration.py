#!/usr/bin/env python3
"""
测试Flask应用中的LLM集成
验证修复后的异步调用在Flask环境中是否正常工作
"""

import asyncio
import json
import sys
import time
from pathlib import Path
from flask import Flask, request, jsonify

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger_utils import setup_logger
from services.llm_service import llm_service


class MockSearchResult:
    """模拟搜索结果"""
    def __init__(self):
        self.success = True
        self.query = "测试查询"
        self.translated_query = "test query"
        self.session_id = "test_session"
        self.timestamp = time.time()
        self.total_time = 1.0
        self.google_results = []
        self.reddit_posts = []
        self.commenters_history = {
            "test_user": {
                "programming": {
                    "posts": ["Test post"],
                    "comments": ["Test comment"]
                }
            }
        }


class FlaskLLMTester:
    """Flask LLM集成测试器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.app = Flask(__name__)
        self.setup_routes()
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.route('/test-llm', methods=['POST'])
        def test_llm():
            """测试LLM分析接口"""
            try:
                data = request.get_json()
                query = data.get('query', '测试查询')
                
                # 模拟搜索结果
                mock_result = MockSearchResult()
                mock_result.query = query
                
                # 执行LLM分析（使用修复后的方法）
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    self.logger.info("🧠 开始执行LLM分析...")
                    llm_analysis_start = time.time()
                    
                    # 使用修复后的方法：loop.run_until_complete 而不是 asyncio.run
                    llm_analysis_results = loop.run_until_complete(
                        self._run_llm_analysis(mock_result)
                    )
                    
                    llm_analysis_time = time.time() - llm_analysis_start
                    self.logger.info(f"✅ LLM分析完成，耗时: {llm_analysis_time:.2f}秒")
                    
                    return jsonify({
                        "success": True,
                        "query": query,
                        "llm_analysis": llm_analysis_results,
                        "analysis_time": llm_analysis_time
                    })
                    
                except Exception as e:
                    self.logger.error(f"⚠️ LLM分析失败: {e}")
                    return jsonify({
                        "success": False,
                        "error": str(e)
                    }), 500
                finally:
                    loop.close()
                    
            except Exception as e:
                self.logger.error(f"请求处理失败: {e}")
                return jsonify({
                    "success": False,
                    "error": str(e)
                }), 500
        
        @self.app.route('/health', methods=['GET'])
        def health():
            """健康检查"""
            return jsonify({
                "status": "healthy",
                "llm_configured": llm_service.configured
            })
    
    async def _run_llm_analysis(self, search_result):
        """执行LLM分析（简化版）"""
        llm_results = {
            "similarity_analysis": {},
            "motivation_analysis": {},
            "analysis_summary": {},
            "success": False,
            "error": None
        }
        
        try:
            # 检查LLM服务配置
            if not llm_service.configured:
                llm_results["error"] = "LLM服务未配置"
                return llm_results
            
            # 简化的分析流程
            self.logger.info("📊 执行简化的LLM分析...")
            
            # 测试基本文本生成
            response = await llm_service.generate_text(
                prompt="简单分析：用户对编程的兴趣",
                max_tokens=100,
                temperature=0.3
            )
            
            # 测试查询翻译
            translation = await llm_service.translate_query_to_english(search_result.query)
            
            # 构建结果
            llm_results["similarity_analysis"] = {
                "test_user": {
                    "similar_subreddits": ["programming", "python"],
                    "analysis": response[:100] if response else "无分析结果"
                }
            }
            
            llm_results["motivation_analysis"] = {
                "test_user": {
                    "motivation": "学习和分享编程知识",
                    "engagement_level": "高"
                }
            }
            
            llm_results["analysis_summary"] = {
                "total_users": 1,
                "query_translation": translation,
                "analysis_quality": "良好"
            }
            
            llm_results["success"] = True
            
            return llm_results
            
        except Exception as e:
            self.logger.error(f"LLM分析失败: {e}")
            llm_results["error"] = str(e)
            return llm_results
    
    def run_test_server(self, port=5555):
        """运行测试服务器"""
        self.logger.info(f"🚀 启动Flask LLM测试服务器: http://127.0.0.1:{port}")
        self.app.run(host='127.0.0.1', port=port, debug=False)


def test_flask_integration():
    """测试Flask集成"""
    import requests
    import threading
    import time
    
    logger = setup_logger(__name__)
    logger.info("🧪 开始Flask LLM集成测试")
    
    # 创建测试器
    tester = FlaskLLMTester()
    
    # 在后台线程中启动Flask服务器
    def run_server():
        tester.run_test_server(port=5556)
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    
    # 等待服务器启动
    time.sleep(3)
    
    try:
        # 测试健康检查
        logger.info("测试健康检查...")
        health_response = requests.get("http://127.0.0.1:5556/health", timeout=10)
        logger.info(f"健康检查响应: {health_response.json()}")
        
        # 测试LLM分析
        logger.info("测试LLM分析...")
        test_data = {"query": "如何学习Python编程"}
        llm_response = requests.post(
            "http://127.0.0.1:5556/test-llm", 
            json=test_data, 
            timeout=60
        )
        
        if llm_response.status_code == 200:
            result = llm_response.json()
            logger.info(f"✅ LLM分析测试成功: {result['success']}")
            logger.info(f"分析耗时: {result.get('analysis_time', 0):.2f}秒")
            return True
        else:
            logger.error(f"❌ LLM分析测试失败: {llm_response.status_code}")
            logger.error(f"错误信息: {llm_response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Flask集成测试失败: {e}")
        return False


if __name__ == "__main__":
    try:
        success = test_flask_integration()
        if success:
            print("\n🎉 Flask LLM集成测试成功！修复有效！")
            sys.exit(0)
        else:
            print("\n💥 Flask LLM集成测试失败！")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
        sys.exit(1)
