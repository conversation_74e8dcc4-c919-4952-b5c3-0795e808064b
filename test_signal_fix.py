#!/usr/bin/env python3
"""
测试信号处理修复
验证修复后的异步调用是否能正常工作
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger_utils import setup_logger
from services.llm_service import llm_service


async def test_llm_analysis_simulation():
    """模拟LLM分析功能测试"""
    logger = setup_logger(__name__)
    logger.info("🧪 开始模拟LLM分析测试")
    
    try:
        # 测试基本文本生成
        logger.info("测试基本文本生成...")
        response = await llm_service.generate_text(
            prompt="简单回答：什么是AI？",
            max_tokens=50,
            temperature=0.3
        )
        logger.info(f"✅ 文本生成成功，长度: {len(response) if response else 0}")
        
        # 测试查询翻译
        logger.info("测试查询翻译...")
        translation = await llm_service.translate_query_to_english("测试查询")
        logger.info(f"✅ 查询翻译成功: {translation}")
        
        # 测试批量相似性筛选
        logger.info("测试批量相似性筛选...")
        similarity_result = await llm_service.filter_similar_subreddits_batch(
            user_subreddits=["programming", "python"],
            target_subreddits=["coding", "javascript"],
            user_id="test_user"
        )
        logger.info(f"✅ 相似性筛选成功，结果: {similarity_result}")
        
        logger.info("🎉 所有LLM分析测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ LLM分析测试失败: {e}")
        return False


def test_asyncio_run_in_thread():
    """测试在线程中使用asyncio.run()是否会出现signal错误"""
    import threading
    
    logger = setup_logger(__name__)
    logger.info("🧪 测试在线程中使用asyncio.run()")
    
    def thread_function():
        try:
            # 这应该会导致signal错误（修复前）
            result = asyncio.run(test_llm_analysis_simulation())
            logger.info(f"✅ 线程中asyncio.run()成功: {result}")
            return result
        except Exception as e:
            logger.error(f"❌ 线程中asyncio.run()失败: {e}")
            return False
    
    # 在新线程中运行
    thread = threading.Thread(target=thread_function)
    thread.start()
    thread.join()
    
    logger.info("线程测试完成")


def test_event_loop_in_thread():
    """测试在线程中使用事件循环（修复后的方法）"""
    import threading
    
    logger = setup_logger(__name__)
    logger.info("🧪 测试在线程中使用事件循环")
    
    def thread_function():
        try:
            # 创建新的事件循环（修复后的方法）
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(test_llm_analysis_simulation())
            
            loop.close()
            logger.info(f"✅ 线程中事件循环成功: {result}")
            return result
        except Exception as e:
            logger.error(f"❌ 线程中事件循环失败: {e}")
            return False
    
    # 在新线程中运行
    thread = threading.Thread(target=thread_function)
    thread.start()
    thread.join()
    
    logger.info("事件循环测试完成")


async def main():
    """主函数"""
    logger = setup_logger(__name__)
    logger.info("🚀 开始信号处理修复测试")
    
    # 测试1: 主线程中的正常异步调用
    logger.info("\n=== 测试1: 主线程中的正常异步调用 ===")
    success1 = await test_llm_analysis_simulation()
    
    # 测试2: 线程中使用asyncio.run()（应该失败）
    logger.info("\n=== 测试2: 线程中使用asyncio.run() ===")
    test_asyncio_run_in_thread()
    
    # 测试3: 线程中使用事件循环（修复后的方法）
    logger.info("\n=== 测试3: 线程中使用事件循环 ===")
    test_event_loop_in_thread()
    
    logger.info("\n📊 信号处理修复测试完成")
    
    if success1:
        logger.info("🎉 主要功能测试通过！")
        return True
    else:
        logger.error("💥 主要功能测试失败！")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            print("\n✅ 信号处理修复验证成功！")
            sys.exit(0)
        else:
            print("\n❌ 信号处理修复验证失败！")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
        sys.exit(1)
