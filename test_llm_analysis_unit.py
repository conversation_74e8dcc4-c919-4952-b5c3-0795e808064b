#!/usr/bin/env python3
"""
LLM分析功能单元测试
用于验证LLM分析功能在隔离环境下是否正常工作
"""

import asyncio
import json
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger_utils import setup_logger
from services.llm_service import llm_service


class LLMAnalysisUnitTest:
    """LLM分析功能单元测试类"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests": [],
            "summary": {
                "total": 0,
                "passed": 0,
                "failed": 0
            }
        }
    
    def add_test_result(self, test_name, success, details=None, error=None):
        """添加测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details,
            "error": str(error) if error else None
        }
        self.test_results["tests"].append(result)
        self.test_results["summary"]["total"] += 1
        if success:
            self.test_results["summary"]["passed"] += 1
        else:
            self.test_results["summary"]["failed"] += 1
    
    async def test_llm_service_initialization(self):
        """测试LLM服务初始化"""
        test_name = "LLM服务初始化测试"
        self.logger.info(f"开始 {test_name}")
        
        try:
            # 检查LLM服务是否已配置
            if not llm_service.configured:
                self.add_test_result(test_name, False, error="LLM服务未配置")
                return False
            
            # 检查基本属性
            stats = llm_service.get_stats()
            details = {
                "configured": llm_service.configured,
                "stats": stats
            }
            
            self.add_test_result(test_name, True, details=details)
            self.logger.info(f"✅ {test_name} 通过")
            return True
            
        except Exception as e:
            self.add_test_result(test_name, False, error=e)
            self.logger.error(f"❌ {test_name} 失败: {e}")
            return False
    
    async def test_basic_text_generation(self):
        """测试基本文本生成功能"""
        test_name = "基本文本生成测试"
        self.logger.info(f"开始 {test_name}")
        
        try:
            prompt = "请简单回答：什么是人工智能？"
            
            start_time = time.time()
            response = await llm_service.generate_text(
                prompt=prompt,
                max_tokens=100,
                temperature=0.3
            )
            duration = time.time() - start_time
            
            if response and len(response.strip()) > 0:
                details = {
                    "prompt": prompt,
                    "response_length": len(response),
                    "duration": duration,
                    "response_preview": response[:100] + "..." if len(response) > 100 else response
                }
                self.add_test_result(test_name, True, details=details)
                self.logger.info(f"✅ {test_name} 通过，耗时: {duration:.2f}秒")
                return True
            else:
                self.add_test_result(test_name, False, error="生成的文本为空")
                return False
                
        except Exception as e:
            self.add_test_result(test_name, False, error=e)
            self.logger.error(f"❌ {test_name} 失败: {e}")
            return False
    
    async def test_query_translation(self):
        """测试查询翻译功能"""
        test_name = "查询翻译测试"
        self.logger.info(f"开始 {test_name}")
        
        try:
            chinese_query = "如何提高工作效率"
            
            start_time = time.time()
            english_query = await llm_service.translate_query_to_english(chinese_query)
            duration = time.time() - start_time
            
            if english_query and english_query != chinese_query:
                details = {
                    "chinese_query": chinese_query,
                    "english_query": english_query,
                    "duration": duration
                }
                self.add_test_result(test_name, True, details=details)
                self.logger.info(f"✅ {test_name} 通过，耗时: {duration:.2f}秒")
                return True
            else:
                self.add_test_result(test_name, False, error="翻译结果无效")
                return False
                
        except Exception as e:
            self.add_test_result(test_name, False, error=e)
            self.logger.error(f"❌ {test_name} 失败: {e}")
            return False
    
    async def test_subreddit_similarity_analysis(self):
        """测试subreddit相似性分析"""
        test_name = "Subreddit相似性分析测试"
        self.logger.info(f"开始 {test_name}")
        
        try:
            # 模拟测试数据
            user_subreddits = ["programming", "python", "webdev"]
            target_subreddits = ["coding", "javascript", "technology"]
            
            start_time = time.time()
            result = await llm_service.analyze_subreddit_similarity_batch(
                user_id="test_user",
                user_subreddits=user_subreddits,
                target_subreddits=target_subreddits
            )
            duration = time.time() - start_time
            
            if result and "user_id" in result:
                details = {
                    "user_subreddits": user_subreddits,
                    "target_subreddits": target_subreddits,
                    "result_keys": list(result.keys()),
                    "duration": duration
                }
                self.add_test_result(test_name, True, details=details)
                self.logger.info(f"✅ {test_name} 通过，耗时: {duration:.2f}秒")
                return True
            else:
                self.add_test_result(test_name, False, error="分析结果格式无效")
                return False
                
        except Exception as e:
            self.add_test_result(test_name, False, error=e)
            self.logger.error(f"❌ {test_name} 失败: {e}")
            return False
    
    async def test_comment_motivation_analysis(self):
        """测试评论动机分析"""
        test_name = "评论动机分析测试"
        self.logger.info(f"开始 {test_name}")
        
        try:
            # 模拟测试数据
            user_id = "test_user"
            target_subreddit = "programming"
            target_post = "How to learn Python effectively?"
            user_comment = "I recommend starting with basic syntax and practicing daily."
            similar_subreddits_data = [
                {
                    "subreddit": "python",
                    "posts": ["Python tutorial", "Best practices"],
                    "comments": ["Great advice", "Thanks for sharing"],
                    "user_engagement": "high"
                }
            ]
            user_overview = {
                "subreddits": ["python", "programming"],
                "user_type": "experienced",
                "activity_level": "high"
            }
            
            start_time = time.time()
            result = await llm_service.analyze_user_comment_motivation(
                user_id=user_id,
                target_subreddit=target_subreddit,
                target_post=target_post,
                user_comment=user_comment,
                similar_subreddits_data=similar_subreddits_data,
                user_overview=user_overview
            )
            duration = time.time() - start_time
            
            if result and "user_id" in result:
                details = {
                    "user_id": user_id,
                    "target_subreddit": target_subreddit,
                    "result_keys": list(result.keys()),
                    "duration": duration
                }
                self.add_test_result(test_name, True, details=details)
                self.logger.info(f"✅ {test_name} 通过，耗时: {duration:.2f}秒")
                return True
            else:
                self.add_test_result(test_name, False, error="分析结果格式无效")
                return False
                
        except Exception as e:
            self.add_test_result(test_name, False, error=e)
            self.logger.error(f"❌ {test_name} 失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        self.logger.info("🧪 开始LLM分析功能单元测试")
        start_time = time.time()
        
        # 运行所有测试
        tests = [
            self.test_llm_service_initialization(),
            self.test_basic_text_generation(),
            self.test_query_translation(),
            self.test_subreddit_similarity_analysis(),
            self.test_comment_motivation_analysis()
        ]
        
        results = await asyncio.gather(*tests, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # 统计结果
        summary = self.test_results["summary"]
        self.logger.info(f"📊 测试完成，总耗时: {total_time:.2f}秒")
        self.logger.info(f"📈 测试统计: 总计 {summary['total']}, 通过 {summary['passed']}, 失败 {summary['failed']}")
        
        # 保存测试结果
        self.save_test_results()
        
        return summary["failed"] == 0
    
    def save_test_results(self):
        """保存测试结果"""
        try:
            output_dir = Path("test_results")
            output_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = output_dir / f"llm_analysis_unit_test_{timestamp}.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"📄 测试结果已保存到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")


async def main():
    """主函数"""
    tester = LLMAnalysisUnitTest()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 所有LLM分析单元测试通过！")
        sys.exit(0)
    else:
        print("\n💥 部分LLM分析单元测试失败，请检查配置。")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
