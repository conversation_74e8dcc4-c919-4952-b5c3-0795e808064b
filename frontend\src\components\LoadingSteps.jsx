import { useState, useEffect } from 'react'
import { Search, Users, Brain, Lightbulb, Check, Loader } from 'lucide-react'

const LoadingSteps = ({ currentStep = 0, onComplete }) => {
  const [animatedStep, setAnimatedStep] = useState(0)

  const steps = [
    {
      id: 'google-search',
      icon: Search,
      title: 'Google搜索中...',
      description: '正在搜索相关Reddit讨论',
      duration: 2000
    },
    {
      id: 'reddit-fetch',
      icon: Users,
      title: 'Reddit数据抓取中...',
      description: '获取用户评论和讨论内容',
      duration: 3000
    },
    {
      id: 'user-analysis',
      icon: Brain,
      title: '分析评论者历史发言...',
      description: '深度分析用户背景和观点倾向',
      duration: 4000
    },
    {
      id: 'motivation-modeling',
      icon: Lightbulb,
      title: '筛选最优回答 + 动机建模...',
      description: '为你找到最懂你的回答',
      duration: 2000
    }
  ]

  useEffect(() => {
    if (currentStep > animatedStep) {
      const timer = setTimeout(() => {
        setAnimatedStep(currentStep)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [currentStep, animatedStep])

  const getStepStatus = (index) => {
    if (index < animatedStep) return 'completed'
    if (index === animatedStep) return 'current'
    return 'pending'
  }

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* 主标题 */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-semibold text-gray-800 mb-2">
          正在为你找懂你的人
        </h2>
        <p className="text-gray-600">
          深度分析Reddit用户背景，筛选最有价值的回答
        </p>
      </div>

      {/* 进度步骤 */}
      <div className="space-y-6">
        {steps.map((step, index) => {
          const status = getStepStatus(index)
          const Icon = step.icon
          
          return (
            <div
              key={step.id}
              className={`flex items-center p-4 rounded-lg transition-all duration-500 ${
                status === 'current' 
                  ? 'bg-primary-50 border border-primary-200 shadow-md transform scale-105' 
                  : status === 'completed'
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-gray-50 border border-gray-200 opacity-60'
              }`}
            >
              {/* 步骤图标 */}
              <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                status === 'completed'
                  ? 'bg-green-500 text-white'
                  : status === 'current'
                    ? 'bg-primary-500 text-white'
                    : 'bg-gray-300 text-gray-500'
              }`}>
                {status === 'completed' ? (
                  <Check className="w-6 h-6" />
                ) : status === 'current' ? (
                  <Loader className="w-6 h-6 animate-spin" />
                ) : (
                  <Icon className="w-6 h-6" />
                )}
              </div>

              {/* 步骤内容 */}
              <div className="ml-4 flex-1">
                <h3 className={`font-medium ${
                  status === 'current' ? 'text-primary-700' : 
                  status === 'completed' ? 'text-green-700' : 'text-gray-500'
                }`}>
                  {step.title}
                </h3>
                <p className={`text-sm mt-1 ${
                  status === 'current' ? 'text-primary-600' :
                  status === 'completed' ? 'text-green-600' : 'text-gray-400'
                }`}>
                  {step.description}
                </p>
              </div>

              {/* 进度指示器 */}
              {status === 'current' && (
                <div className="flex-shrink-0 w-2 h-8 bg-primary-200 rounded-full overflow-hidden">
                  <div className="w-full bg-primary-500 rounded-full animate-pulse-slow h-full"></div>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* 底部提示 */}
      <div className="text-center mt-8">
        <p className="text-sm text-gray-500">
          预计剩余时间: {Math.max(0, (steps.length - animatedStep) * 2)} 秒
        </p>
      </div>
    </div>
  )
}

export default LoadingSteps 