#!/usr/bin/env python3
"""
测试Flask路由定义
验证路由是否正确注册
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from flask import Flask, jsonify, request
from flask_cors import CORS

def create_test_app():
    """创建测试Flask应用"""
    app = Flask(__name__)
    CORS(app)
    
    @app.route('/test', methods=['GET'])
    def test():
        """简单测试接口"""
        return jsonify({
            "message": "Flask app is running",
            "timestamp": time.time(),
            "mode": "test"
        })
    
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """健康检查接口"""
        try:
            return jsonify({
                "status": "healthy",
                "service": "CogBridges API",
                "timestamp": time.time()
            })
        except Exception as e:
            return jsonify({
                "status": "error",
                "error": str(e)
            }), 500
    
    @app.route('/api/history', methods=['GET'])
    def get_search_history():
        """获取搜索历史接口"""
        try:
            # 返回模拟的搜索历史数据
            history_data = [
                {
                    "id": "1",
                    "query": "如何度过创业低谷期",
                    "timestamp": time.time() - 3600,  # 1小时前
                    "resultsCount": 5,
                    "searchTime": 43.01,
                    "success": True
                },
                {
                    "id": "2", 
                    "query": "30岁转行程序员来得及吗",
                    "timestamp": time.time() - 86400,  # 1天前
                    "resultsCount": 3,
                    "searchTime": 35.2,
                    "success": True
                }
            ]
            
            return jsonify(history_data)
            
        except Exception as e:
            return jsonify({
                "error": f"获取搜索历史失败: {str(e)}"
            }), 500
    
    @app.route('/api/bookmarks', methods=['GET'])
    def get_bookmarks():
        """获取收藏列表接口"""
        try:
            # 返回模拟的收藏数据
            bookmarks_data = [
                {
                    "id": "bookmark_1",
                    "query": "如何度过创业低谷期",
                    "title": "创业低谷期的心理调适",
                    "content": "创业路上遇到低谷是正常的...",
                    "source": "r/entrepreneur",
                    "timestamp": time.time() - 7200,  # 2小时前
                    "tags": ["创业", "心理健康", "经验分享"]
                }
            ]
            
            return jsonify(bookmarks_data)
            
        except Exception as e:
            return jsonify({
                "error": f"获取收藏列表失败: {str(e)}"
            }), 500
    
    @app.route('/api/bookmarks', methods=['POST'])
    def add_bookmark():
        """添加收藏接口"""
        try:
            data = request.get_json()
            
            # 在实际应用中，这里应该将数据保存到数据库
            bookmark_id = f"bookmark_{int(time.time())}"
            
            return jsonify({
                "success": True,
                "bookmark_id": bookmark_id,
                "message": "收藏添加成功"
            })
            
        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"添加收藏失败: {str(e)}"
            }), 500
    
    @app.route('/api/bookmarks/<bookmark_id>', methods=['DELETE'])
    def remove_bookmark(bookmark_id):
        """删除收藏接口"""
        try:
            # 在实际应用中，这里应该从数据库中删除对应的收藏记录
            
            return jsonify({
                "success": True,
                "message": f"收藏 {bookmark_id} 删除成功"
            })
            
        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"删除收藏失败: {str(e)}"
            }), 500
    
    return app

def test_routes():
    """测试路由定义"""
    print("🧪 测试Flask路由定义")
    print("=" * 50)
    
    app = create_test_app()
    
    # 列出所有路由
    print("📋 已注册的路由:")
    for rule in app.url_map.iter_rules():
        methods = ', '.join(rule.methods - {'HEAD', 'OPTIONS'})
        print(f"  {rule.rule} [{methods}]")
    
    print("\n🚀 启动测试服务器...")
    print("访问 http://localhost:5555 测试路由")
    
    try:
        app.run(host='127.0.0.1', port=5555, debug=True)
    except KeyboardInterrupt:
        print("\n👋 测试服务器已停止")

if __name__ == "__main__":
    test_routes()
