import { useState, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import LoadingSteps from '../components/LoadingSteps'

const LoadingPage = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0)

  const query = location.state?.query || '搜索查询'
  const sessionId = location.state?.sessionId
  const searchResult = location.state?.searchResult

  // 处理搜索结果
  useEffect(() => {
    if (!location.state?.query) {
      // 如果没有查询参数，重定向到首页
      navigate('/')
      return
    }

    // 如果已经有搜索结果，直接跳转到结果页
    if (searchResult && searchResult.success) {
      console.log('已获取搜索结果，直接跳转:', searchResult)
      
      // 模拟一个简短的加载过程让用户看到进度
      const steps = [
        { duration: 500 },
        { duration: 500 },
        { duration: 500 },
        { duration: 500 }
      ]

      let totalDuration = 0
      steps.forEach((step, index) => {
        setTimeout(() => {
          setCurrentStep(index)
          setProgress(((index + 1) / steps.length) * 100)
        }, totalDuration)
        totalDuration += step.duration
      })

      // 完成后跳转到结果页
      setTimeout(() => {
        navigate('/results', { 
          state: { 
            query,
            searchResult,
            searchTime: totalDuration
          } 
        })
      }, totalDuration)
      
      return
    }

    // 如果没有结果，显示错误并返回首页
    setTimeout(() => {
      console.error('没有搜索结果数据')
      alert('搜索结果获取失败，请重试')
      navigate('/')
    }, 2000)

  }, [location.state, navigate, searchResult, query])

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">C</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-800">CogBridges</h1>
              <p className="text-sm text-gray-500">正在分析搜索结果...</p>
            </div>
          </div>
          
          <div className="text-right">
            <p className="text-sm text-gray-600">搜索内容</p>
            <p className="font-medium text-gray-800 max-w-md truncate">"{query}"</p>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="flex-1 flex items-center justify-center px-6 py-12">
        <div className="w-full max-w-3xl">
          {/* 总体进度条 */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">分析进度</span>
              <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>

          {/* 加载步骤 */}
          <LoadingSteps 
            currentStep={currentStep}
            onComplete={() => {
              // 这个回调不再需要，因为useEffect已经处理了跳转
            }}
          />

          {/* 底部提示信息 */}
          <div className="mt-12 text-center">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-blue-800 mb-2">
                正在分析已获取的数据
              </h3>
              <p className="text-blue-700 leading-relaxed">
                我们已经完成了数据收集，正在进行深度分析，为你筛选出最有价值、最贴近你需求的回答。
                每一个推荐都基于用户的真实经历和深度思考。
              </p>
            </div>
          </div>

          {/* 取消按钮 */}
          <div className="mt-8 text-center">
            <button
              onClick={() => navigate('/')}
              className="px-6 py-2 text-gray-500 hover:text-gray-700 transition-colors"
            >
              返回首页
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}

export default LoadingPage 