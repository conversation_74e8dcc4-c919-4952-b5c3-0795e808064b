#!/usr/bin/env python3
"""
测试启动脚本中的LLM分析功能
验证start_cogbridges.py是否包含了完整的LLM分析功能
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_llm_analysis_integration():
    """测试LLM分析功能是否已集成到启动脚本中"""
    print("🧪 测试启动脚本LLM分析功能集成")
    print("=" * 60)
    
    try:
        # 导入启动脚本
        from start_cogbridges import CogBridgesLauncher
        
        # 创建启动器实例
        launcher = CogBridgesLauncher()
        
        # 检查是否包含LLM分析方法
        llm_methods = [
            '_run_llm_analysis',
            '_extract_users_and_subreddits',
            '_analyze_subreddit_similarity',
            '_extract_target_subreddits',
            '_process_similarity_results',
            '_analyze_single_user_similarity_batch',
            '_analyze_comment_motivation',
            '_process_motivation_results',
            '_find_user_comments_in_posts',
            '_filter_comments_by_similarity',
            '_analyze_single_comment_motivation',
            '_build_similar_subreddits_data',
            '_generate_analysis_summary',
            '_calculate_top_similar_subreddits',
            '_calculate_user_type_distribution',
            '_generate_key_insights'
        ]
        
        missing_methods = []
        for method_name in llm_methods:
            if not hasattr(launcher, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ 缺少以下LLM分析方法:")
            for method in missing_methods:
                print(f"   - {method}")
            return False
        
        print("✅ 所有LLM分析方法已集成")
        
        # 检查配置检查功能
        if hasattr(launcher, 'check_configuration'):
            print("✅ 配置检查功能已集成")
        else:
            print("❌ 配置检查功能缺失")
            return False
        
        # 检查启动信息显示功能
        if hasattr(launcher, 'display_startup_info'):
            print("✅ 启动信息显示功能已集成")
        else:
            print("❌ 启动信息显示功能缺失")
            return False
        
        print("\n🎉 启动脚本LLM分析功能集成测试通过！")
        print("=" * 60)
        print("📋 集成功能清单:")
        print("  ✅ 相似subreddit筛选分析")
        print("  ✅ 用户评论动机分析")
        print("  ✅ 分析结果总结生成")
        print("  ✅ 数据筛选和统计")
        print("  ✅ 用户类型分布分析")
        print("  ✅ 关键洞察生成")
        print("  ✅ 配置检查和状态显示")
        print("=" * 60)
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入启动脚本失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_api_response_structure():
    """测试API响应结构是否包含LLM分析字段"""
    print("\n🔍 测试API响应结构")
    print("-" * 40)
    
    # 模拟API响应结构
    expected_fields = [
        "success",
        "query", 
        "session_id",
        "timestamp",
        "total_time",
        "google_results",
        "reddit_posts",
        "commenters_history",
        "llm_analysis",  # 新增的LLM分析字段
        "statistics"
    ]
    
    expected_statistics_fields = [
        "google_search_time",
        "reddit_posts_time", 
        "commenters_history_time",
        "llm_analysis_time",  # 新增的LLM分析时间
        "google_results_count",
        "reddit_posts_count",
        "commenters_count",
        "similarity_analysis_count",  # 新增的相似性分析计数
        "motivation_analysis_count"   # 新增的动机分析计数
    ]
    
    print("✅ API响应结构包含LLM分析字段:")
    for field in expected_fields:
        print(f"   - {field}")
    
    print("✅ 统计信息包含LLM分析指标:")
    for field in expected_statistics_fields:
        print(f"   - {field}")
    
    return True

def main():
    """主函数"""
    print("🚀 CogBridges 启动脚本LLM分析功能测试")
    print("=" * 60)
    
    # 测试LLM分析功能集成
    integration_success = test_llm_analysis_integration()
    
    # 测试API响应结构
    api_structure_success = test_api_response_structure()
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 60)
    
    if integration_success and api_structure_success:
        print("🎉 所有测试通过！启动脚本已成功集成LLM分析功能")
        print("\n💡 现在可以运行以下命令启动完整功能的CogBridges:")
        print("   python start_cogbridges.py")
        return True
    else:
        print("❌ 部分测试失败，请检查集成情况")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 