#!/usr/bin/env python3
"""
CogBridges - 统一启动脚本
同时启动后端API服务器和前端Web界面的单一启动脚本
"""

import sys
import os
import subprocess
import threading
import time
import webbrowser
import asyncio
from pathlib import Path
import signal
import atexit

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger_utils import get_logger


class CogBridgesLauncher:
    """CogBridges统一启动器"""
    
    def __init__(self):
        """初始化启动器"""
        self.logger = get_logger(__name__)
        self.backend_process = None
        self.frontend_server = None
        self.frontend_process = None  # React前端进程
        self.running = False
        
        # 注册退出处理
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查依赖...")
        
        # 定义包名和对应的导入名
        package_mappings = {
            'flask': 'flask',
            'flask_cors': 'flask_cors', 
            'requests': 'requests',
            'praw': 'praw',
            'asyncpraw': 'asyncpraw',
            'tenacity': 'tenacity',
            'python-dotenv': 'dotenv'
        }
        
        missing_packages = []
        
        for package, import_name in package_mappings.items():
            try:
                __import__(import_name)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ 缺少以下依赖包:")
            for package in missing_packages:
                print(f"   - {package}")
            print(f"\n💡 请运行以下命令安装依赖:")
            print(f"   pip install -r requirements.txt")
            return False
        
        print("✅ 所有依赖已安装")
        return True
    
    def check_configuration(self):
        """检查配置"""
        print("🔧 检查配置...")
        
        # 检查必要的配置项
        required_configs = [
            ('GOOGLE_API_KEY', config.GOOGLE_API_KEY),
            ('GOOGLE_SEARCH_ENGINE_ID', config.GOOGLE_SEARCH_ENGINE_ID),
            ('REDDIT_CLIENT_ID', config.REDDIT_CLIENT_ID),
            ('REDDIT_CLIENT_SECRET', config.REDDIT_CLIENT_SECRET)
        ]
        
        missing_configs = []
        
        for name, value in required_configs:
            if not value or value == "your_api_key_here":
                missing_configs.append(name)
        
        if missing_configs:
            print(f"❌ 缺少以下配置:")
            for config_name in missing_configs:
                print(f"   - {config_name}")
            print(f"\n💡 请在config.py中配置这些参数")
            return False
        
        # 检查LLM服务配置（可选）
        if not config.replicate_configured:
            print("⚠️ Replicate API未配置，LLM分析功能将不可用")
        else:
            print("✅ LLM服务配置检查通过")
        
        print("✅ 配置检查通过")
        return True
    
    async def _run_llm_analysis(self, search_result):
        """执行LLM分析功能 - 从测试脚本移植"""
        from services.llm_service import llm_service
        
        llm_results = {
            "similarity_analysis": {},
            "motivation_analysis": {},
            "analysis_summary": {},
            "success": False,
            "error": None
        }
        
        try:
            # 检查LLM服务配置
            if not llm_service.configured:
                llm_results["error"] = "LLM服务未配置"
                return llm_results
            
            # 提取用户和subreddit信息
            users_data = self._extract_users_and_subreddits(search_result)
            print(f"📊 提取到 {len(users_data)} 个用户数据")
            
            if not users_data:
                llm_results["error"] = "未找到有效的用户数据"
                return llm_results
            
            # 执行相似subreddit筛选
            print("🔍 执行相似subreddit筛选...")
            similarity_results = await self._analyze_subreddit_similarity(users_data, search_result)
            llm_results["similarity_analysis"] = similarity_results
            
            # 执行用户评论动机分析
            print("🎯 执行用户评论动机分析...")
            motivation_results = await self._analyze_comment_motivation(users_data, search_result, similarity_results)
            llm_results["motivation_analysis"] = motivation_results
            
            # 生成分析总结
            llm_results["analysis_summary"] = self._generate_analysis_summary(similarity_results, motivation_results)
            llm_results["success"] = True
            
            return llm_results
            
        except Exception as e:
            self.logger.error(f"LLM分析失败: {e}")
            llm_results["error"] = str(e)
            return llm_results
    
    def _extract_users_and_subreddits(self, search_result):
        """从搜索结果中提取用户和subreddit信息"""
        users_data = []
        
        if not search_result.commenters_history:
            return users_data
        
        for username, user_data in search_result.commenters_history.items():
            if not isinstance(user_data, dict):
                continue
                
            # 获取用户的subreddits列表
            user_subreddits = []
            if '_metadata' in user_data:
                metadata = user_data['_metadata']
                user_subreddits = metadata.get('subreddits', [])
            
            # 如果没有metadata，从用户数据中提取subreddits
            if not user_subreddits:
                user_subreddits = [sr for sr in user_data.keys() if sr != '_metadata']
            
            if user_subreddits:
                users_data.append({
                    "username": username,
                    "user_subreddits": user_subreddits,
                    "user_data": user_data
                })
        
        return users_data
    
    async def _analyze_subreddit_similarity(self, users_data, search_result):
        """分析subreddit相似性"""
        from services.llm_service import llm_service
        
        similarity_results = {}
        
        # 从Reddit帖子中提取目标subreddits
        target_subreddits = self._extract_target_subreddits(search_result)
        target_subreddit_list = list(target_subreddits)
        print(f"🎯 目标subreddits: {target_subreddit_list}")
        
        # 使用批量分析，每个用户只调用一次LLM
        tasks = []
        for user_info in users_data:
            tasks.append(self._analyze_single_user_similarity_batch(
                user_info["username"],
                user_info["user_subreddits"],
                target_subreddit_list
            ))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            similarity_results = self._process_similarity_results(results)
        
        return similarity_results
    
    def _extract_target_subreddits(self, search_result):
        """提取目标subreddits"""
        target_subreddits = set()
        if search_result.reddit_posts:
            for post_data in search_result.reddit_posts:
                if 'post' in post_data:
                    subreddit = post_data['post'].get('subreddit')
                    if subreddit:
                        target_subreddits.add(subreddit)
        
        # 如果没有找到目标subreddits，使用默认的AI相关subreddits
        if not target_subreddits:
            target_subreddits = {'ClaudeAI', 'ChatGPT', 'OpenAI', 'artificial'}
        
        return target_subreddits
    
    def _process_similarity_results(self, results):
        """处理相似性分析结果"""
        similarity_results = {}
        
        for result in results:
            if isinstance(result, Exception):
                continue
                
            username = result.get("username")
            batch_results = result.get("batch_results", {})
            original_subreddits = result.get("original_subreddits", [])
            
            if username not in similarity_results:
                similarity_results[username] = {
                    "original_subreddits": original_subreddits,
                    "target_analysis": {},
                    "all_similar_subreddits": set()
                }
            
            # 将批量结果转换为优化后的格式
            for target_subreddit, similar_subreddits in batch_results.items():
                similarity_results[username]["target_analysis"][target_subreddit] = {
                    "similar_subreddits": similar_subreddits,
                    "similarity_count": len(similar_subreddits)
                }
                # 收集所有相似subreddits
                similarity_results[username]["all_similar_subreddits"].update(similar_subreddits)
        
        # 将set转换为list以便JSON序列化
        for username in similarity_results:
            similarity_results[username]["all_similar_subreddits"] = list(similarity_results[username]["all_similar_subreddits"])
            similarity_results[username]["total_similar_count"] = len(similarity_results[username]["all_similar_subreddits"])
        
        return similarity_results
    
    async def _analyze_single_user_similarity_batch(self, username, user_subreddits, target_subreddits):
        """批量分析单个用户的subreddit相似性"""
        from services.llm_service import llm_service
        
        try:
            batch_results = await llm_service.filter_similar_subreddits_batch(
                user_subreddits=user_subreddits,
                target_subreddits=target_subreddits,
                user_id=username
            )
            
            return {
                "username": username,
                "original_subreddits": user_subreddits,
                "batch_results": batch_results
            }
            
        except Exception as e:
            self.logger.error(f"用户{username}的批量相似性分析失败: {e}")
            return {
                "username": username,
                "error": str(e)
            }
    
    async def _analyze_comment_motivation(self, users_data, search_result, similarity_results):
        """分析用户评论动机"""
        from services.llm_service import llm_service
        
        motivation_results = {}
        data_statistics = {}
        
        # 构建用户评论分析任务
        tasks = []
        
        for user_info in users_data:
            username = user_info["username"]
            user_data = user_info["user_data"]
            
            # 查找用户的评论和对应的帖子
            user_comments = self._find_user_comments_in_posts(username, search_result)
            
            # 记录原始数据统计
            original_posts_count = len(set(comment["post"]["id"] for comment in user_comments))
            original_comments_count = len(user_comments)
            
            # 根据相似性分析结果筛选评论
            filtered_comments = self._filter_comments_by_similarity(
                user_comments, username, similarity_results
            )
            
            # 记录筛选后数据统计
            filtered_posts_count = len(set(comment["post"]["id"] for comment in filtered_comments))
            filtered_comments_count = len(filtered_comments)
            
            # 保存统计信息
            data_statistics[username] = {
                "original_posts_count": original_posts_count,
                "original_comments_count": original_comments_count,
                "filtered_posts_count": filtered_posts_count,
                "filtered_comments_count": filtered_comments_count,
                "filter_ratio": {
                    "posts": filtered_posts_count / original_posts_count if original_posts_count > 0 else 0,
                    "comments": filtered_comments_count / original_comments_count if original_comments_count > 0 else 0
                }
            }
            
            for comment_info in filtered_comments:
                tasks.append(self._analyze_single_comment_motivation(
                    username, comment_info, user_info["user_subreddits"], user_data
                ))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            motivation_results = self._process_motivation_results(results)
        
        # 将统计信息添加到结果中
        motivation_results["data_statistics"] = data_statistics
        
        return motivation_results
    
    def _process_motivation_results(self, results):
        """处理动机分析结果"""
        motivation_results = {}
        
        for result in results:
            if isinstance(result, Exception):
                continue
                
            username = result.get("user_id")
            if username not in motivation_results:
                motivation_results[username] = []
            
            motivation_results[username].append(result)
        
        return motivation_results
    
    def _find_user_comments_in_posts(self, username, search_result):
        """在帖子中查找用户的评论"""
        user_comments = []
        
        if not search_result.reddit_posts:
            return user_comments
        
        for post_data in search_result.reddit_posts:
            if 'post' not in post_data or 'comments' not in post_data:
                continue
                
            post = post_data['post']
            comments = post_data['comments']
            
            # 查找该用户在这个帖子中的评论
            for comment in comments:
                if comment.get('author') == username:
                    user_comments.append({
                        "post": post,
                        "comment": comment,
                        "subreddit": post.get('subreddit')
                    })
        
        return user_comments
    
    def _filter_comments_by_similarity(self, user_comments, username, similarity_results):
        """根据相似性分析结果筛选评论"""
        if username not in similarity_results:
            return user_comments
        
        user_similarity = similarity_results[username]
        similar_subreddits = user_similarity.get("all_similar_subreddits", [])
        
        if not similar_subreddits:
            return user_comments
        
        # 筛选出在相似subreddits中的评论
        filtered_comments = []
        for comment_info in user_comments:
            comment_subreddit = comment_info["subreddit"]
            # 检查评论的subreddit是否在相似subreddits列表中
            if any(similar_sub in comment_subreddit or comment_subreddit in similar_sub 
                   for similar_sub in similar_subreddits):
                filtered_comments.append(comment_info)
        
        return filtered_comments
    
    async def _analyze_single_comment_motivation(self, username, comment_info, user_subreddits, user_data):
        """分析单个评论的动机"""
        from services.llm_service import llm_service
        
        try:
            # 构建相似subreddits数据
            similar_subreddits_data = self._build_similar_subreddits_data(user_subreddits, user_data)
            
            # 构建用户概览
            user_overview = {
                "subreddits": user_subreddits,
                "user_type": "unknown",
                "activity_level": "medium"
            }
            
            # 调用LLM分析
            result = await llm_service.analyze_user_comment_motivation(
                user_id=username,
                target_subreddit=comment_info["subreddit"],
                target_post=comment_info["post"],
                user_comment=comment_info["comment"],
                similar_subreddits_data=similar_subreddits_data,
                user_overview=user_overview
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"用户{username}评论动机分析失败: {e}")
            return {
                "user_id": username,
                "error": str(e)
            }
    
    def _build_similar_subreddits_data(self, user_subreddits, user_data):
        """构建相似subreddits数据"""
        similar_subreddits_data = []
        for subreddit in user_subreddits:
            if subreddit in user_data:
                sub_data = user_data[subreddit]
                similar_subreddits_data.append({
                    "subreddit": subreddit,
                    "posts": sub_data.get("posts", []),
                    "comments": sub_data.get("comments", []),
                    "user_engagement": "high" if len(sub_data.get("comments", [])) > 5 else "medium"
                })
        return similar_subreddits_data
    
    def _generate_analysis_summary(self, similarity_results, motivation_results):
        """生成分析总结"""
        # 处理动机分析结果，排除data_statistics
        data_statistics = motivation_results.pop("data_statistics", {})
        user_motivations = {k: v for k, v in motivation_results.items() if k != "data_statistics"}
        
        summary = {
            "total_users_analyzed": len(similarity_results),
            "total_motivations_analyzed": sum(len(motivations) for motivations in user_motivations.values()),
            "top_similar_subreddits": {},
            "user_type_distribution": {},
            "data_filtering_statistics": data_statistics,
            "key_insights": []
        }
        
        # 统计最常见的相似subreddits
        summary["top_similar_subreddits"] = self._calculate_top_similar_subreddits(similarity_results)
        
        # 分析用户类型分布
        summary["user_type_distribution"] = self._calculate_user_type_distribution(user_motivations)
        
        # 生成关键洞察
        summary["key_insights"] = self._generate_key_insights(summary)
        
        # 恢复data_statistics到motivation_results
        motivation_results["data_statistics"] = data_statistics
        
        return summary
    
    def _calculate_top_similar_subreddits(self, similarity_results):
        """计算最常见的相似subreddits"""
        subreddit_counts = {}
        for user_results in similarity_results.values():
            target_analysis = user_results.get("target_analysis", {})
            for target_results in target_analysis.values():
                for subreddit in target_results.get("similar_subreddits", []):
                    subreddit_counts[subreddit] = subreddit_counts.get(subreddit, 0) + 1
        
        return dict(sorted(subreddit_counts.items(), key=lambda x: x[1], reverse=True)[:10])
    
    def _calculate_user_type_distribution(self, motivation_results):
        """计算用户类型分布"""
        user_types = {}
        for user_motivations in motivation_results.values():
            for motivation in user_motivations:
                user_profile = motivation.get("user_profile", "")
                if "开发" in user_profile or "程序" in user_profile:
                    user_types["developer"] = user_types.get("developer", 0) + 1
                elif "初学" in user_profile or "学习" in user_profile:
                    user_types["learner"] = user_types.get("learner", 0) + 1
                elif "研究" in user_profile or "科学" in user_profile:
                    user_types["researcher"] = user_types.get("researcher", 0) + 1
                else:
                    user_types["other"] = user_types.get("other", 0) + 1
        
        return user_types
    
    def _generate_key_insights(self, summary):
        """生成关键洞察"""
        insights = []
        
        if summary["total_users_analyzed"] > 0:
            insights.append(f"分析了{summary['total_users_analyzed']}个用户的subreddit相似性")
        
        if summary["total_motivations_analyzed"] > 0:
            insights.append(f"深度分析了{summary['total_motivations_analyzed']}条评论的动机")
        
        if summary["top_similar_subreddits"]:
            top_subreddit = list(summary["top_similar_subreddits"].keys())[0]
            insights.append(f"最常见的相似subreddit是{top_subreddit}")
        
        # 添加数据筛选统计洞察
        data_stats = summary.get("data_filtering_statistics", {})
        if data_stats:
            total_original_posts = sum(stats.get("original_posts_count", 0) for stats in data_stats.values())
            total_filtered_posts = sum(stats.get("filtered_posts_count", 0) for stats in data_stats.values())
            total_original_comments = sum(stats.get("original_comments_count", 0) for stats in data_stats.values())
            total_filtered_comments = sum(stats.get("filtered_comments_count", 0) for stats in data_stats.values())
            
            if total_original_posts > 0:
                post_filter_ratio = total_filtered_posts / total_original_posts
                insights.append(f"相似性筛选将帖子数量从{total_original_posts}减少到{total_filtered_posts} ({post_filter_ratio:.1%})")
            
            if total_original_comments > 0:
                comment_filter_ratio = total_filtered_comments / total_original_comments
                insights.append(f"相似性筛选将评论数量从{total_original_comments}减少到{total_filtered_comments} ({comment_filter_ratio:.1%})")
        
        return insights
    
    def start_backend_api(self):
        """启动后端API服务器"""
        print("🚀 启动后端API服务器...")
        
        try:
            # 创建Flask应用
            from flask import Flask, jsonify, request
            from flask_cors import CORS
            from services.cogbridges_service import CogBridgesService
            
            app = Flask(__name__)
            CORS(app)
            
            # 添加一个简单的测试路由
            @app.route('/test', methods=['GET'])
            def test():
                """简单测试接口"""
                return jsonify({
                    "message": "Flask app is running",
                    "timestamp": time.time(),
                    "cogbridges_service_status": "initialized" if cogbridges_service else "not_initialized"
                })
            
            @app.route('/api/health', methods=['GET'])
            def health_check():
                """健康检查接口"""
                try:
                    return jsonify({
                        "status": "healthy",
                        "service": "CogBridges API",
                        "timestamp": time.time()
                    })
                except Exception as e:
                    return jsonify({
                        "status": "error",
                        "error": str(e)
                    }), 500
            
            # 初始化CogBridges服务
            cogbridges_service = None
            try:
                cogbridges_service = CogBridgesService()
                print("✅ CogBridges服务初始化成功")
            except Exception as e:
                self.logger.error(f"CogBridges服务初始化失败: {e}")
                print(f"⚠️ CogBridges服务初始化失败: {e}")
                print("📝 继续启动，但某些功能可能不可用")
            
            @app.route('/api/search', methods=['POST'])
            def search():
                """统一搜索接口 - 默认使用增强功能"""
                try:
                    # 检查服务是否可用
                    if not cogbridges_service:
                        return jsonify({
                            "success": False,
                            "error": "CogBridges服务未初始化"
                        }), 503
                    
                    data = request.get_json()
                    query = data.get('query', '')

                    if not query:
                        return jsonify({
                            "success": False,
                            "error": "查询参数不能为空"
                        }), 400

                    # 执行搜索（同步调用异步函数）
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        # 执行搜索
                        result = loop.run_until_complete(
                            cogbridges_service.search(query)
                        )

                        # 执行LLM分析（如果配置了LLM服务）
                        llm_analysis_results = None
                        llm_analysis_time = 0.0
                        
                        if hasattr(result, 'commenters_history') and result.commenters_history:
                            try:
                                print("🧠 开始执行LLM分析...")
                                llm_analysis_start = time.time()
                                
                                # 执行LLM分析
                                llm_analysis_results = asyncio.run(self._run_llm_analysis(result))
                                
                                llm_analysis_time = time.time() - llm_analysis_start
                                print(f"✅ LLM分析完成，耗时: {llm_analysis_time:.2f}秒")
                                
                            except Exception as e:
                                print(f"⚠️ LLM分析失败: {e}")
                                llm_analysis_results = {"error": str(e)}
                        
                        # 转换结果为JSON格式
                        response_data = {
                            "success": result.success,
                            "query": result.query,
                            "translated_query": result.translated_query,
                            "session_id": result.session_id,
                            "timestamp": result.timestamp.isoformat(),
                            "total_time": result.total_time,
                            "google_results": result.google_results,
                            "reddit_posts": result.reddit_posts,
                            "commenters_history": result.commenters_history,
                            "llm_analysis": llm_analysis_results,
                            "statistics": {
                                "translation_time": result.translation_time,
                                "google_search_time": result.google_search_time,
                                "reddit_posts_time": result.reddit_posts_time,
                                "commenters_history_time": result.commenters_history_time,
                                "llm_analysis_time": llm_analysis_time,
                                "google_results_count": len(result.google_results) if result.google_results else 0,
                                "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                                "commenters_count": len(result.commenters_history) if result.commenters_history else 0,
                                "similarity_analysis_count": len(llm_analysis_results.get("similarity_analysis", {})) if llm_analysis_results else 0,
                                "motivation_analysis_count": sum(len(motivations) for motivations in llm_analysis_results.get("motivation_analysis", {}).values()) if llm_analysis_results else 0
                            }
                        }



                        if not result.success:
                            response_data["error"] = result.error_message

                        return jsonify(response_data)

                    finally:
                        loop.close()

                except Exception as e:
                    self.logger.error(f"搜索请求处理失败: {e}")
                    return jsonify({
                        "success": False,
                        "error": f"服务器内部错误: {str(e)}"
                    }), 500

            @app.route('/api/status', methods=['GET'])
            def get_status():
                """获取服务状态接口"""
                try:
                    if not cogbridges_service:
                        return jsonify({
                            "service": "CogBridges API",
                            "timestamp": time.time(),
                            "status": "service_unavailable",
                            "error": "CogBridges服务未初始化"
                        }), 503
                    
                    stats = cogbridges_service.get_statistics()

                    # 检查各个服务的状态
                    status_info = {
                        "service": "CogBridges API",
                        "timestamp": time.time(),
                        "version": "2.0.0",
                        "features": {
                            "basic_search": True,
                            "enhanced_comments": True,
                            "llm_analysis": True
                        },
                        "services": {
                            "google_search": bool(stats.get("google_stats", {}).get("configured", False)),
                            "reddit_api": bool(stats.get("reddit_stats", {}).get("configured", False)),
                            "llm_service": bool(stats.get("llm_stats", {}).get("configured", False))
                        },
                        "statistics": stats
                    }

                    return jsonify(status_info)

                except Exception as e:
                    self.logger.error(f"状态查询失败: {e}")
                    return jsonify({
                        "service": "CogBridges API",
                        "timestamp": time.time(),
                        "error": f"状态查询失败: {str(e)}"
                    }), 500
            
            # 启动Flask应用 - 使用直接启动而不是线程
            print(f"📍 正在启动Flask服务器: {config.HOST}:{config.PORT}")
            
            # 检查端口是否可用
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((config.HOST, config.PORT))
            sock.close()
            
            if result == 0:
                print(f"⚠️ 端口 {config.PORT} 已被占用，尝试使用其他端口")
                config.PORT = config.PORT + 10
                print(f"🔄 改用端口: {config.PORT}")
            
            # 在单独线程中启动Flask应用
            def run_flask():
                try:
                    print(f"🔧 Flask配置: host={config.HOST}, port={config.PORT}")
                    app.run(
                        host=config.HOST,
                        port=config.PORT,
                        debug=False,  # 关闭调试模式避免重复启动
                        threaded=True,
                        use_reloader=False  # 关闭reloader避免线程冲突
                    )
                except Exception as e:
                    print(f"❌ Flask应用启动失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            backend_thread = threading.Thread(target=run_flask, daemon=True)
            backend_thread.start()
            
            # 等待服务器启动
            print("⏳ 等待Flask服务器启动...")
            time.sleep(8)  # 增加等待时间
            
            # 测试API是否正常
            import requests
            
            # 首先测试简单路由
            try:
                print(f"🔍 正在测试简单路由: http://{config.HOST}:{config.PORT}/test")
                test_response = requests.get(f"http://{config.HOST}:{config.PORT}/test", timeout=10)
                print(f"📡 简单路由响应状态码: {test_response.status_code}")
                if test_response.status_code == 200:
                    print(f"📄 简单路由响应内容: {test_response.text}")
                    print("✅ 简单路由测试成功")
                else:
                    print(f"❌ 简单路由响应异常: {test_response.status_code}")
                    print(f"📄 响应内容: {test_response.text[:200]}...")
            except requests.RequestException as e:
                print(f"❌ 简单路由连接失败: {e}")
                return False
            
            # 然后测试健康检查路由
            try:
                print(f"🔍 正在测试API连接: http://{config.HOST}:{config.PORT}/api/health")
                response = requests.get(f"http://{config.HOST}:{config.PORT}/api/health", timeout=10)
                print(f"📡 API响应状态码: {response.status_code}")
                if response.status_code == 200:
                    print(f"✅ 后端API服务器启动成功: http://{config.HOST}:{config.PORT}")
                    return True
                else:
                    print(f"❌ 后端API服务器响应异常: {response.status_code}")
                    print(f"📄 响应内容: {response.text[:200]}...")
                    return False
            except requests.RequestException as e:
                print(f"❌ 后端API服务器连接失败: {e}")
                print(f"💡 可能的原因: 端口被占用、防火墙阻止、或服务器启动失败")
                return False
                
        except Exception as e:
            self.logger.error(f"后端API服务器启动失败: {e}")
            print(f"❌ 后端API服务器启动失败: {e}")
            return False
    
    def start_frontend_server(self):
        """启动React前端开发服务器"""
        print("🌐 启动React前端开发服务器...")
        
        try:
            import subprocess
            import sys
            import os
            
            # 设置前端目录
            frontend_dir = project_root / "frontend"
            if not frontend_dir.exists():
                print(f"❌ React前端目录不存在: {frontend_dir}")
                print("💡 请先运行 'npm create vite@latest frontend -- --template react' 创建前端项目")
                return False
            
            # 检查package.json是否存在
            package_json = frontend_dir / "package.json"
            if not package_json.exists():
                print(f"❌ 前端项目未初始化: {package_json}")
                print("💡 请先在前端目录运行 'npm install' 安装依赖")
                return False
            
            # 检查node_modules是否存在
            node_modules = frontend_dir / "node_modules"
            if not node_modules.exists():
                print("📦 检测到前端依赖未安装，正在安装...")
                try:
                    result = subprocess.run(
                        "npm install",
                        cwd=str(frontend_dir),
                        capture_output=True,
                        text=True,
                        timeout=120,
                        shell=True
                    )
                    if result.returncode != 0:
                        print(f"❌ 前端依赖安装失败: {result.stderr}")
                        return False
                    print("✅ 前端依赖安装完成")
                except subprocess.TimeoutExpired:
                    print("❌ 前端依赖安装超时")
                    return False
                except Exception as e:
                    print(f"❌ 前端依赖安装失败: {e}")
                    return False
            
            # 前端端口为API端口+1
            frontend_port = config.PORT + 1
            
            # 检查前端端口是否可用
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(("", frontend_port))
            sock.close()
            
            if result == 0:
                print(f"⚠️ 前端端口 {frontend_port} 已被占用，跳过前端服务器启动")
                return True  # 返回True避免阻止整体启动
            
            def run_frontend_server():
                try:
                    # 启动Vite开发服务器
                    env = os.environ.copy()
                    env['VITE_API_URL'] = f"http://localhost:{config.PORT}"
                    
                    # 使用shell=True来确保能找到npm命令
                    cmd = f"npm run dev -- --port {frontend_port} --host"
                    process = subprocess.Popen(
                        cmd,
                        cwd=str(frontend_dir),
                        env=env,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,
                        text=True,
                        shell=True
                    )
                    
                    self.frontend_process = process
                    print(f"✅ React前端开发服务器启动成功: http://localhost:{frontend_port}")
                    
                    # 等待进程结束
                    while self.running and process.poll() is None:
                        time.sleep(1)
                        
                except Exception as e:
                    print(f"⚠️ React前端服务器启动失败: {e}")
            
            frontend_thread = threading.Thread(target=run_frontend_server, daemon=True)
            frontend_thread.start()
            
            # 等待服务器启动
            time.sleep(3)
            return True
            
        except Exception as e:
            self.logger.error(f"React前端服务器启动失败: {e}")
            print(f"❌ React前端服务器启动失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        frontend_port = config.PORT + 1
        url = f"http://localhost:{frontend_port}"
        
        print(f"🌐 正在打开浏览器: {url}")
        
        try:
            # 检查前端服务是否可用
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(("localhost", frontend_port))
            sock.close()
            
            if result == 0:
                webbrowser.open(url)
                print("✅ 浏览器已打开")
            else:
                print(f"⚠️ 前端服务器未启动，请手动访问: {url}")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"💡 请手动访问: {url}")
    
    def display_startup_info(self):
        """显示启动信息"""
        print("\n" + "=" * 60)
        print("🎉 CogBridges 启动成功！")
        print("=" * 60)
        print(f"🔗 后端API地址: http://{config.HOST}:{config.PORT}")
        print(f"🌐 前端Web地址: http://localhost:{config.PORT + 1}")
        print(f"📚 API文档: http://{config.HOST}:{config.PORT}/api/health")
        print("-" * 60)
        print("💡 使用说明:")
        print("  1. 在Web界面中输入搜索查询")
        print("  2. 系统将自动执行Google搜索 → Reddit数据获取 → 用户分析")
        print("  3. 执行LLM相似subreddit筛选和用户评论动机分析")
        print("  4. 查看完整的分析结果和用户画像")
        print("-" * 60)
        print("🧠 LLM分析功能:")
        if config.replicate_configured:
            print("  ✅ 相似subreddit筛选: 已启用")
            print("  ✅ 用户评论动机分析: 已启用")
        else:
            print("  ⚠️ LLM分析功能: 未配置Replicate API")
        print("-" * 60)
        print("🛑 按 Ctrl+C 停止服务")
        print("=" * 60)
    
    def run(self):
        """运行启动器"""
        print("🌟 CogBridges 统一启动器")
        print("=" * 60)
        
        # 检查依赖和配置
        if not self.check_dependencies():
            return False
        
        if not self.check_configuration():
            return False
        
        # 创建Flask应用
        flask_app = self.start_backend_api_direct()
        if not flask_app:
            return False
        
        # 启动前端Web服务器
        if not self.start_frontend_server():
            return False
        
        # 显示启动信息
        self.display_startup_info()
        
        # 打开浏览器
        self.open_browser()
        
        # 启动Flask应用
        print(f"🚀 正在启动Flask服务器: {config.HOST}:{config.PORT}")
        print("⏳ Flask应用即将启动，请稍候...")
        
        try:
            flask_app.run(
                host=config.HOST,
                port=config.PORT,
                debug=False,  # 关闭调试模式避免重复启动
                threaded=True
            )
        except Exception as e:
            print(f"❌ Flask应用运行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
    
    def start_backend_api_direct(self):
        """直接启动后端API服务器（非线程模式）"""
        print("🚀 启动后端API服务器（直接模式）...")
        
        try:
            # 创建Flask应用
            from flask import Flask, jsonify, request
            from flask_cors import CORS
            from services.cogbridges_service import CogBridgesService
            
            app = Flask(__name__)
            CORS(app)
            
            # 添加一个简单的测试路由
            @app.route('/test', methods=['GET'])
            def test():
                """简单测试接口"""
                return jsonify({
                    "message": "Flask app is running",
                    "timestamp": time.time(),
                    "mode": "direct"
                })
            
            @app.route('/api/health', methods=['GET'])
            def health_check():
                """健康检查接口"""
                try:
                    return jsonify({
                        "status": "healthy",
                        "service": "CogBridges API",
                        "timestamp": time.time()
                    })
                except Exception as e:
                    return jsonify({
                        "status": "error",
                        "error": str(e)
                    }), 500
            
            # 初始化CogBridges服务
            cogbridges_service = None
            try:
                cogbridges_service = CogBridgesService()
                print("✅ CogBridges服务初始化成功")
            except Exception as e:
                self.logger.error(f"CogBridges服务初始化失败: {e}")
                print(f"⚠️ CogBridges服务初始化失败: {e}")
                print("📝 继续启动，但某些功能可能不可用")
            
            @app.route('/api/search', methods=['POST'])
            def search():
                """统一搜索接口 - 默认使用增强功能"""
                try:
                    # 检查服务是否可用
                    if not cogbridges_service:
                        return jsonify({
                            "success": False,
                            "error": "CogBridges服务未初始化"
                        }), 503
                    
                    data = request.get_json()
                    query = data.get('query', '')

                    if not query:
                        return jsonify({
                            "success": False,
                            "error": "查询参数不能为空"
                        }), 400

                    # 执行搜索（同步调用异步函数）
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        # 执行搜索
                        result = loop.run_until_complete(
                            cogbridges_service.search(query)
                        )

                        # 执行LLM分析（如果配置了LLM服务）
                        llm_analysis_results = None
                        llm_analysis_time = 0.0
                        
                        if hasattr(result, 'commenters_history') and result.commenters_history:
                            try:
                                print("🧠 开始执行LLM分析...")
                                llm_analysis_start = time.time()
                                
                                # 执行LLM分析 - 使用launcher实例
                                launcher = CogBridgesLauncher()
                                llm_analysis_results = asyncio.run(launcher._run_llm_analysis(result))
                                
                                llm_analysis_time = time.time() - llm_analysis_start
                                print(f"✅ LLM分析完成，耗时: {llm_analysis_time:.2f}秒")
                                
                            except Exception as e:
                                print(f"⚠️ LLM分析失败: {e}")
                                llm_analysis_results = {"error": str(e)}
                        
                        # 转换结果为JSON格式
                        response_data = {
                            "success": result.success,
                            "query": result.query,
                            "translated_query": result.translated_query,
                            "session_id": result.session_id,
                            "timestamp": result.timestamp.isoformat(),
                            "total_time": result.total_time,
                            "google_results": result.google_results,
                            "reddit_posts": result.reddit_posts,
                            "commenters_history": result.commenters_history,
                            "llm_analysis": llm_analysis_results,
                            "statistics": {
                                "google_search_time": result.google_search_time,
                                "reddit_posts_time": result.reddit_posts_time,
                                "commenters_history_time": result.commenters_history_time,
                                "llm_analysis_time": llm_analysis_time,
                                "google_results_count": len(result.google_results) if result.google_results else 0,
                                "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                                "commenters_count": len(result.commenters_history) if result.commenters_history else 0,
                                "similarity_analysis_count": len(llm_analysis_results.get("similarity_analysis", {})) if llm_analysis_results else 0,
                                "motivation_analysis_count": sum(len(motivations) for motivations in llm_analysis_results.get("motivation_analysis", {}).values()) if llm_analysis_results else 0
                            }
                        }



                        if not result.success:
                            response_data["error"] = result.error_message

                        return jsonify(response_data)

                    finally:
                        loop.close()

                except Exception as e:
                    self.logger.error(f"搜索请求处理失败: {e}")
                    return jsonify({
                        "success": False,
                        "error": f"服务器内部错误: {str(e)}"
                    }), 500

            @app.route('/api/status', methods=['GET'])
            def get_status():
                """获取服务状态接口"""
                try:
                    if not cogbridges_service:
                        return jsonify({
                            "service": "CogBridges API",
                            "timestamp": time.time(),
                            "status": "service_unavailable",
                            "error": "CogBridges服务未初始化"
                        }), 503
                    
                    stats = cogbridges_service.get_statistics()

                    # 检查各个服务的状态
                    status_info = {
                        "service": "CogBridges API",
                        "timestamp": time.time(),
                        "version": "2.0.0",
                        "features": {
                            "basic_search": True,
                            "enhanced_comments": True,
                            "llm_analysis": True
                        },
                        "services": {
                            "google_search": bool(stats.get("google_stats", {}).get("configured", False)),
                            "reddit_api": bool(stats.get("reddit_stats", {}).get("configured", False)),
                            "llm_service": bool(stats.get("llm_stats", {}).get("configured", False))
                        },
                        "statistics": stats
                    }

                    return jsonify(status_info)

                except Exception as e:
                    self.logger.error(f"状态查询失败: {e}")
                    return jsonify({
                        "service": "CogBridges API",
                        "timestamp": time.time(),
                        "error": f"状态查询失败: {str(e)}"
                    }), 500
            
            # 返回Flask应用对象，供主线程启动
            return app
                
        except Exception as e:
            self.logger.error(f"后端API服务器启动失败: {e}")
            print(f"❌ 后端API服务器启动失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 正在清理资源...")
        self.running = False
        
        # 清理React前端进程
        if hasattr(self, 'frontend_process') and self.frontend_process:
            try:
                # 终止React开发服务器进程
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ React前端服务器已停止")
            except Exception as e:
                print(f"⚠️ React前端服务器关闭时出错: {e}")
        
        # 清理旧的前端服务器（兼容性）
        if hasattr(self, 'frontend_server') and self.frontend_server:
            try:
                self.frontend_server.shutdown()
                self.frontend_server.server_close()
                print("✅ 旧前端服务器已停止")
            except Exception as e:
                print(f"⚠️ 旧前端服务器关闭时出错: {e}")
        
        # 清理后端进程
        if hasattr(self, 'backend_process') and self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ 后端进程已停止")
            except Exception as e:
                print(f"⚠️ 后端进程关闭时出错: {e}")
        
        print("👋 CogBridges 已停止")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 收到信号 {signum}，正在停止服务...")
        
        # 设置超时清理
        import threading
        cleanup_thread = threading.Thread(target=self.cleanup, daemon=True)
        cleanup_thread.start()
        
        # 等待清理完成或超时
        cleanup_thread.join(timeout=3)
        
        # 强制退出
        print("🚪 强制退出...")
        os._exit(0)


def main():
    """主函数"""
    launcher = CogBridgesLauncher()
    
    try:
        success = launcher.run()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止...")
        launcher.cleanup()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
