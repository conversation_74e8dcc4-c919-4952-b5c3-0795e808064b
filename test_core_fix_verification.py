#!/usr/bin/env python3
"""
核心修复验证测试
验证signal错误修复的核心功能
"""

import asyncio
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger_utils import setup_logger


async def simple_async_task():
    """简单的异步任务"""
    await asyncio.sleep(0.1)
    return "异步任务完成"


def test_asyncio_run_vs_loop_run_until_complete():
    """测试 asyncio.run() vs loop.run_until_complete() 在线程中的行为"""
    logger = setup_logger(__name__)
    logger.info("🧪 测试异步调用方法对比")
    
    results = {
        "asyncio_run": {"success": False, "error": None},
        "loop_run_until_complete": {"success": False, "error": None}
    }
    
    # 测试1: 在线程中使用 asyncio.run()
    def test_asyncio_run():
        try:
            result = asyncio.run(simple_async_task())
            results["asyncio_run"]["success"] = True
            results["asyncio_run"]["result"] = result
            logger.info("✅ 线程中 asyncio.run() 成功")
        except Exception as e:
            results["asyncio_run"]["error"] = str(e)
            logger.error(f"❌ 线程中 asyncio.run() 失败: {e}")
    
    # 测试2: 在线程中使用 loop.run_until_complete()
    def test_loop_run_until_complete():
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(simple_async_task())
            loop.close()
            results["loop_run_until_complete"]["success"] = True
            results["loop_run_until_complete"]["result"] = result
            logger.info("✅ 线程中 loop.run_until_complete() 成功")
        except Exception as e:
            results["loop_run_until_complete"]["error"] = str(e)
            logger.error(f"❌ 线程中 loop.run_until_complete() 失败: {e}")
    
    # 在线程中运行测试
    thread1 = threading.Thread(target=test_asyncio_run)
    thread2 = threading.Thread(target=test_loop_run_until_complete)
    
    thread1.start()
    thread2.start()
    
    thread1.join()
    thread2.join()
    
    return results


def simulate_flask_request_handling():
    """模拟Flask请求处理"""
    logger = setup_logger(__name__)
    logger.info("🌐 模拟Flask请求处理")
    
    def handle_request():
        """模拟请求处理函数"""
        try:
            # 模拟Flask应用中的异步调用（修复前的方式）
            logger.info("测试修复前的方式（可能失败）...")
            try:
                # 这可能会在某些环境中导致signal错误
                result1 = asyncio.run(simple_async_task())
                logger.info(f"修复前方式成功: {result1}")
            except Exception as e:
                logger.warning(f"修复前方式失败（预期）: {e}")
            
            # 模拟修复后的方式
            logger.info("测试修复后的方式...")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result2 = loop.run_until_complete(simple_async_task())
                logger.info(f"✅ 修复后方式成功: {result2}")
                return True
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"❌ 请求处理失败: {e}")
            return False
    
    # 在线程中模拟请求处理（Flask的工作线程）
    success = False
    def worker():
        nonlocal success
        success = handle_request()
    
    thread = threading.Thread(target=worker)
    thread.start()
    thread.join()
    
    return success


def test_signal_handling_fix():
    """测试信号处理修复"""
    logger = setup_logger(__name__)
    logger.info("📡 测试信号处理修复")
    
    # 测试主线程中的信号处理
    import signal
    
    def dummy_signal_handler(signum, frame):
        logger.info(f"收到信号: {signum}")
    
    try:
        # 在主线程中注册信号处理器（应该成功）
        if threading.current_thread() is threading.main_thread():
            signal.signal(signal.SIGUSR1, dummy_signal_handler)
            logger.info("✅ 主线程中信号处理器注册成功")
            
            # 恢复默认处理器
            signal.signal(signal.SIGUSR1, signal.SIG_DFL)
            return True
        else:
            logger.warning("当前不在主线程中")
            return False
            
    except Exception as e:
        logger.error(f"❌ 信号处理器注册失败: {e}")
        return False


def main():
    """主函数"""
    logger = setup_logger(__name__)
    logger.info("🚀 开始核心修复验证测试")
    
    all_passed = True
    
    # 测试1: 异步调用方法对比
    logger.info("\n=== 测试1: 异步调用方法对比 ===")
    async_results = test_asyncio_run_vs_loop_run_until_complete()
    
    if async_results["loop_run_until_complete"]["success"]:
        logger.info("✅ loop.run_until_complete() 方法正常工作")
    else:
        logger.error("❌ loop.run_until_complete() 方法失败")
        all_passed = False
    
    # 测试2: 模拟Flask请求处理
    logger.info("\n=== 测试2: 模拟Flask请求处理 ===")
    flask_success = simulate_flask_request_handling()
    
    if flask_success:
        logger.info("✅ Flask请求处理模拟成功")
    else:
        logger.error("❌ Flask请求处理模拟失败")
        all_passed = False
    
    # 测试3: 信号处理修复
    logger.info("\n=== 测试3: 信号处理修复 ===")
    signal_success = test_signal_handling_fix()
    
    if signal_success:
        logger.info("✅ 信号处理修复正常")
    else:
        logger.error("❌ 信号处理修复失败")
        all_passed = False
    
    # 总结
    logger.info("\n📊 核心修复验证测试完成")
    
    if all_passed:
        logger.info("🎉 所有核心修复验证测试通过！")
        print("\n✅ 核心修复验证成功！")
        print("修复要点:")
        print("1. 将 asyncio.run() 替换为 loop.run_until_complete()")
        print("2. 只在主线程中注册信号处理器")
        print("3. 在多线程环境中正确管理事件循环")
        return True
    else:
        logger.error("💥 部分核心修复验证测试失败！")
        print("\n❌ 核心修复验证失败！")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
        sys.exit(1)
