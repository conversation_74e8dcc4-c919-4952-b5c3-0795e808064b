#!/usr/bin/env python3
"""
测试API连接性
验证后端API是否正常运行并可访问
"""

import requests
import time
import sys
from pathlib import Path

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:5000"
    
    endpoints = [
        "/api/health",
        "/api/history", 
        "/api/bookmarks",
        "/test"
    ]
    
    print("🔍 测试API连接性...")
    print(f"基础URL: {base_url}")
    print("=" * 50)
    
    all_success = True
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        try:
            print(f"测试: {endpoint}")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"  ✅ 成功 - 状态码: {response.status_code}")
                try:
                    data = response.json()
                    print(f"  📄 响应: {str(data)[:100]}...")
                except:
                    print(f"  📄 响应: {response.text[:100]}...")
            else:
                print(f"  ⚠️  状态码: {response.status_code}")
                print(f"  📄 响应: {response.text[:100]}...")
                all_success = False
                
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接失败 - 服务器可能未运行")
            all_success = False
        except requests.exceptions.Timeout:
            print(f"  ❌ 请求超时")
            all_success = False
        except Exception as e:
            print(f"  ❌ 错误: {e}")
            all_success = False
        
        print()
    
    return all_success

def test_search_api():
    """测试搜索API"""
    base_url = "http://localhost:5000"
    url = f"{base_url}/api/search"
    
    print("🔍 测试搜索API...")
    print(f"URL: {url}")
    print("=" * 50)
    
    test_data = {
        "query": "测试查询",
        "enhanced": True,
        "llm_analysis": True
    }
    
    try:
        print("发送POST请求...")
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 搜索API正常工作")
            try:
                data = response.json()
                print(f"响应数据: {str(data)[:200]}...")
                return True
            except:
                print(f"响应文本: {response.text[:200]}...")
                return True
        else:
            print(f"⚠️ 搜索API返回错误状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 后端服务器可能未运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时 - 后端处理时间过长")
        return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    print("=" * 50)
    
    # 检查端口是否被占用
    import socket
    
    def check_port(host, port):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except:
            return False
    
    ports_to_check = [5000, 5001]
    
    for port in ports_to_check:
        if check_port('localhost', port):
            print(f"✅ 端口 {port} 正在被使用")
        else:
            print(f"❌ 端口 {port} 未被使用")
    
    print()

def main():
    """主函数"""
    print("🚀 CogBridges API连接性测试")
    print("=" * 60)
    
    # 检查服务器状态
    check_server_status()
    
    # 测试基本API端点
    basic_success = test_api_endpoints()
    
    # 如果基本端点正常，测试搜索API
    if basic_success:
        search_success = test_search_api()
        
        if search_success:
            print("🎉 所有API测试通过！")
            print("\n建议:")
            print("1. 前端应该能正常连接到后端")
            print("2. 检查浏览器控制台是否有其他错误")
            print("3. 确认前端的API_BASE_URL配置正确")
            return True
        else:
            print("💥 搜索API测试失败！")
            return False
    else:
        print("💥 基本API测试失败！")
        print("\n建议:")
        print("1. 确认后端服务器正在运行")
        print("2. 检查start_cogbridges.py是否成功启动")
        print("3. 查看后端控制台是否有错误信息")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
        sys.exit(1)
